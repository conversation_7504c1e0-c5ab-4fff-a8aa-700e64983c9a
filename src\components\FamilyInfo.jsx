import { motion } from 'framer-motion';
import { Users, Heart, Home } from 'lucide-react';

const FamilyInfo = () => {
  const familyData = [
    {
      side: "Nhà Trai",
      icon: <Users className="w-8 h-8" />,
      address: "123 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>",
      parents: [
        { title: "<PERSON>", name: "Ông Nguyễn <PERSON>" },
        { title: "Mẹ", name: "Bà Trần Thị Lan" }
      ],
      color: "from-rose-gold to-champagne",
      emoji: "👨‍👩‍👦"
    },
    {
      side: "Nhà Gái", 
      icon: <Heart className="w-8 h-8" />,
      address: "456 <PERSON>, Đống Đ<PERSON>, Hà Nội",
      parents: [
        { title: "Cha", name: "Ông <PERSON><PERSON>" },
        { title: "Mẹ", name: "<PERSON>à Phạm Thị <PERSON>" }
      ],
      color: "from-sage to-blush",
      emoji: "👨‍👩‍👧"
    }
  ];

  return (
    <section className="py-20 bg-white/50">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-didot text-5xl font-bold text-gray-800 mb-4 tracking-wide">
            Thông Tin Gia Đình
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Trân trọng giới thiệu gia đình hai bên
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {familyData.map((family, index) => (
            <motion.div
              key={family.side}
              initial={{ opacity: 0, x: index === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="relative"
            >
              <div className="wedding-card p-8 text-center relative overflow-hidden">
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${family.color} opacity-10 rounded-3xl`} />
                
                {/* Decorative corner elements */}
                <motion.div
                  className="absolute top-4 right-4 text-3xl"
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                >
                  {family.emoji}
                </motion.div>
                
                <motion.div
                  className="absolute bottom-4 left-4 text-rose-gold/30 text-xl"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                >
                  🌸
                </motion.div>

                <div className="relative z-10">
                  {/* Header */}
                  <motion.div
                    className="flex items-center justify-center mb-6"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="text-rose-gold mr-3">
                      {family.icon}
                    </div>
                    <h3 className="font-didot text-3xl font-bold text-gray-800 tracking-wide">
                      {family.side}
                    </h3>
                  </motion.div>

                  {/* Address */}
                  <motion.div
                    className="mb-8"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-center justify-center mb-2">
                      <Home className="w-5 h-5 text-sage mr-2" />
                      <span className="font-didot text-lg font-medium text-gray-700">
                        Địa chỉ
                      </span>
                    </div>
                    <p className="text-gray-600 font-didot leading-relaxed">
                      {family.address}
                    </p>
                  </motion.div>

                  {/* Parents */}
                  <div className="space-y-4">
                    {family.parents.map((parent, parentIndex) => (
                      <motion.div
                        key={parent.title}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 + parentIndex * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30"
                      >
                        <div className="text-rose-gold font-didot text-sm font-medium mb-1 tracking-wide">
                          {parent.title}
                        </div>
                        <div className="font-didot text-lg font-semibold text-gray-800">
                          {parent.name}
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Decorative divider */}
                  <motion.div
                    className="flex items-center justify-center mt-8"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <div className="w-16 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                    <span className="mx-3 text-champagne text-xl">💕</span>
                    <div className="w-16 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="wedding-card max-w-3xl mx-auto p-8">
            <motion.div
              animate={{ rotate: [0, 5, -5, 0] }}
              transition={{ duration: 4, repeat: Infinity }}
              className="text-4xl mb-4"
            >
              👨‍👩‍👧‍👦
            </motion.div>
            
            <h3 className="font-didot text-2xl font-bold text-gray-800 mb-4 tracking-wide">
              Sự Kết Hợp Của Hai Gia Đình
            </h3>
            <p className="text-gray-600 font-didot leading-relaxed">
              Chúng tôi rất vinh dự được sự chấp thuận và chúc phước từ gia đình hai bên. 
              Đây không chỉ là hôn lễ của hai chúng tôi mà còn là sự kết nối của hai gia đình yêu thương.
            </p>
            
            <motion.div
              className="flex justify-center space-x-3 mt-6 text-2xl"
              animate={{ y: [0, -3, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span>🏠</span>
              <span>💕</span>
              <span>🏠</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FamilyInfo;
