// Lazy loading utility for images
export const lazyLoadImage = (src, placeholder = '') => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(src);
    img.onerror = reject;
    img.src = src;
  });
};

// Preload critical images
export const preloadImages = (imageUrls) => {
  return Promise.all(
    imageUrls.map(url => lazyLoadImage(url))
  );
};

// Optimize image loading with intersection observer
export const createImageObserver = (callback) => {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          callback(entry.target);
        }
      });
    },
    {
      rootMargin: '50px',
      threshold: 0.1,
    }
  );
};
