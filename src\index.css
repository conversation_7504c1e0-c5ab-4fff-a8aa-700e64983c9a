@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400;1,600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    border-color: theme('colors.gray.200');
  }
  body {
    @apply bg-gradient-to-br from-blush via-champagne to-rose-gold font-inter;
    margin: 0;
    min-height: 100vh;
    letter-spacing: 0.025em;
    line-height: 1.6;
  }

  /* Font families */
  .font-dancing {
    font-family: 'Dancing Script', cursive;
  }

  .font-great-vibes {
    font-family: 'Great Vibes', cursive;
  }

  .font-crimson {
    font-family: 'Crimson Text', serif;
  }

  .font-playfair {
    font-family: 'Playfair Display', serif;
  }

  .font-inter {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .wedding-card {
    @apply bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,228,225,0.9) 50%, rgba(247,231,206,0.95) 100%);
    box-shadow:
      0 25px 50px rgba(0,0,0,0.1),
      0 0 0 1px rgba(255,255,255,0.5) inset,
      0 2px 4px rgba(232,180,184,0.2);
  }

  .elegant-text {
    @apply font-playfair text-gray-800 tracking-wide;
  }

  /* Reduced spacing between sections */
  .section-spacing {
    @apply py-12 md:py-16;
  }

  .section-spacing-small {
    @apply py-8 md:py-12;
  }

  .floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
  }

  .heart {
    position: absolute;
    color: #E8B4B8;
    animation: float 6s ease-in-out infinite;
  }

  .heart:nth-child(1) { left: 10%; animation-delay: 0s; }
  .heart:nth-child(2) { left: 20%; animation-delay: 1s; }
  .heart:nth-child(3) { left: 30%; animation-delay: 2s; }
  .heart:nth-child(4) { left: 40%; animation-delay: 3s; }
  .heart:nth-child(5) { left: 50%; animation-delay: 4s; }
  .heart:nth-child(6) { left: 60%; animation-delay: 5s; }
  .heart:nth-child(7) { left: 70%; animation-delay: 6s; }
  .heart:nth-child(8) { left: 80%; animation-delay: 7s; }
  .heart:nth-child(9) { left: 90%; animation-delay: 8s; }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(247,231,206,0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #E8B4B8, #F7E7CE);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #E8B4B8, #9CAF88);
  }

  /* Text selection */
  ::selection {
    background: rgba(232,180,184,0.3);
    color: #374151;
  }

  /* Smooth focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-rose-gold focus:ring-opacity-50;
  }

  /* Glass morphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #E8B4B8 0%, #F7E7CE 50%, #9CAF88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 3D Card Effects */
  .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  /* Smooth transitions */
  * {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  /* Enhanced button styles */
  .btn-primary {
    background: linear-gradient(135deg, #E8B4B8 0%, #F7E7CE 100%);
    box-shadow: 0 4px 15px rgba(232, 180, 184, 0.3);
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(232, 180, 184, 0.4);
  }

  /* Loading animation */
  @keyframes shimmer-loading {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer-loading 1.5s infinite;
  }

  /* Falling Hearts Animation */
  .falling-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
  }

  .falling-heart {
    position: absolute;
    color: rgba(232, 180, 184, 0.6);
    font-size: 20px;
    animation: fall linear infinite;
    user-select: none;
  }

  .falling-heart:nth-child(1) { left: 10%; animation-duration: 8s; animation-delay: 0s; }
  .falling-heart:nth-child(2) { left: 20%; animation-duration: 10s; animation-delay: 1s; }
  .falling-heart:nth-child(3) { left: 30%; animation-duration: 7s; animation-delay: 2s; }
  .falling-heart:nth-child(4) { left: 40%; animation-duration: 9s; animation-delay: 3s; }
  .falling-heart:nth-child(5) { left: 50%; animation-duration: 11s; animation-delay: 4s; }
  .falling-heart:nth-child(6) { left: 60%; animation-duration: 8s; animation-delay: 5s; }
  .falling-heart:nth-child(7) { left: 70%; animation-duration: 10s; animation-delay: 6s; }
  .falling-heart:nth-child(8) { left: 80%; animation-duration: 9s; animation-delay: 7s; }
  .falling-heart:nth-child(9) { left: 90%; animation-duration: 12s; animation-delay: 8s; }

  @keyframes fall {
    0% {
      transform: translateY(-100px) rotate(0deg);
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      transform: translateY(100vh) rotate(360deg);
      opacity: 0;
    }
  }

  /* Floating hearts animation (different from falling) */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-20px) rotate(10deg);
      opacity: 1;
    }
  }
}
