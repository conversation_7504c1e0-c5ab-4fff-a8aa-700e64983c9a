import { motion } from 'framer-motion';
import { Heart, Sparkles } from 'lucide-react';

const Hero = () => {
  const floatingHearts = Array.from({ length: 9 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute text-rose-gold/30"
      style={{
        left: `${10 + i * 10}%`,
        top: `${Math.random() * 80 + 10}%`,
      }}
      animate={{
        y: [0, -20, 0],
        rotate: [0, 10, -10, 0],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        repeat: Infinity,
        delay: i * 0.5,
      }}
    >
      <Heart className="w-4 h-4" />
    </motion.div>
  ));

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-gradient-to-br from-blush/90 via-champagne/90 to-rose-gold/90" />
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-rose-gold/5 animate-pulse" />
        </div>
      </div>
      {/* Floating Hearts Background */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingHearts}
      </div>

      {/* Sparkles Animation */}
      <motion.div
        className="absolute top-20 right-20 text-champagne"
        animate={{
          rotate: 360,
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>

      <motion.div
        className="absolute bottom-20 left-20 text-sage"
        animate={{
          rotate: -360,
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-6 h-6" />
      </motion.div>

      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Save the Date Badge */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <div className="inline-flex items-center bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/30">
            <span className="font-didot text-rose-gold font-medium tracking-wider text-sm">
              SAVE THE DATE
            </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="wedding-card max-w-5xl mx-auto p-8 sm:p-12 md:p-16 relative overflow-hidden"
        >
          {/* Decorative corner flourishes */}
          <div className="absolute top-0 left-0 w-20 h-20 opacity-20">
            <svg viewBox="0 0 100 100" className="w-full h-full text-rose-gold">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute top-0 right-0 w-20 h-20 opacity-20 transform rotate-90">
            <svg viewBox="0 0 100 100" className="w-full h-full text-champagne">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute bottom-0 left-0 w-20 h-20 opacity-20 transform rotate-180">
            <svg viewBox="0 0 100 100" className="w-full h-full text-sage">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute bottom-0 right-0 w-20 h-20 opacity-20 transform rotate-270">
            <svg viewBox="0 0 100 100" className="w-full h-full text-blush">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          {/* Wedding Announcement */}
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-sage font-medium text-lg mb-6 tracking-wider"
          >
            CHÚNG TÔI SẮP KẾT HÔN
          </motion.p>

          {/* Couple Names */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="mb-8"
          >
            <div className="relative">
              <h1 className="font-carolyna text-4xl sm:text-6xl md:text-7xl lg:text-8xl font-bold text-gray-800 mb-4 relative">
                <motion.span
                  className="inline-block relative"
                  whileHover={{
                    scale: 1.05,
                    color: "#E8B4B8",
                    textShadow: "0 0 20px rgba(232, 180, 184, 0.6)"
                  }}
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  Minh
                  <motion.div
                    className="absolute -top-2 -right-2 text-rose-gold text-lg"
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    ✨
                  </motion.div>
                </motion.span>

                <motion.span
                  className="inline-block mx-2 sm:mx-4 md:mx-6 text-rose-gold relative"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  &
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-rose-gold to-champagne opacity-20 rounded-full blur-lg"
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </motion.span>

                <motion.span
                  className="inline-block relative"
                  whileHover={{
                    scale: 1.05,
                    color: "#E8B4B8",
                    textShadow: "0 0 20px rgba(232, 180, 184, 0.6)"
                  }}
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5
                  }}
                >
                  Hương
                  <motion.div
                    className="absolute -top-2 -left-2 text-sage text-lg"
                    animate={{ rotate: [0, -10, 10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                  >
                    💖
                  </motion.div>
                </motion.span>
              </h1>

              {/* Decorative elements */}
              <motion.div
                className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-champagne text-2xl"
                animate={{
                  y: [0, -10, 0],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                ✨ ✨ ✨
              </motion.div>

              <motion.div
                className="absolute -bottom-4 left-1/4 text-rose-gold text-xl"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 8, repeat: Infinity }}
              >
                🌹
              </motion.div>

              <motion.div
                className="absolute -bottom-4 right-1/4 text-sage text-xl"
                animate={{
                  rotate: [360, 0],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 8, repeat: Infinity, delay: 2 }}
              >
                🌿
              </motion.div>
            </div>
          </motion.div>

          {/* Wedding Date */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="mb-8"
          >
            <p className="text-gray-600 text-xl mb-2">Ngày cưới</p>
            <p className="font-serif text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-800">
              15 tháng 8, 2024
            </p>
          </motion.div>

          {/* Location */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="mb-10"
          >
            <p className="text-gray-600 text-lg">
              Trung tâm Hội nghị Quốc gia, Hà Nội
            </p>
          </motion.div>

          {/* CTA Button */}
          <motion.button
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.7 }}
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 30px rgba(232, 180, 184, 0.3)"
            }}
            whileTap={{ scale: 0.95 }}
            className="bg-rose-gold text-white px-8 py-4 rounded-full font-medium text-lg shadow-lg hover:bg-rose-gold/90 transition-all duration-300"
            onClick={() => document.getElementById('rsvp').scrollIntoView({ behavior: 'smooth' })}
          >
            Xác Nhận Tham Dự
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
