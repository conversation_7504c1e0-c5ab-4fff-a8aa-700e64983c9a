import Header from './components/Header'
import Hero from './components/Hero'
import InteractiveCard from './components/InteractiveCard'
import CountdownTimer from './components/CountdownTimer'
import LoveStory from './components/LoveStory'
import FamilyInfo from './components/FamilyInfo'
import Timeline from './components/Timeline'
import LocationMap from './components/LocationMap'
import Gallery from './components/Gallery'
import GiftMoney from './components/GiftMoney'
import Guestbook from './components/Guestbook'
import RSVP from './components/RSVP'
import Footer from './components/Footer'
import BackgroundAnimations from './components/BackgroundAnimations'
import { EnhancedAnimations, SectionDivider } from './components/EnhancedAnimations'
import WelcomeMessage from './components/WelcomeMessage'
import MusicPlayer from './components/MusicPlayer'
import FloatingMenu from './components/FloatingMenu'
import FallingHearts from './components/FallingHearts'
import BackToTop from './components/BackToTop'

function App() {
  return (
    <div className="min-h-screen relative">
      <WelcomeMessage />
      <BackgroundAnimations />
      <EnhancedAnimations />
      <FallingHearts />
      <MusicPlayer />
      <div className="relative z-10">
        <Header />
        <main>
          <Hero />
          <SectionDivider direction="right" />
          <InteractiveCard />
          <SectionDivider direction="left" />
          <CountdownTimer />
          <SectionDivider direction="right" />
          <LoveStory />
          <SectionDivider direction="left" />
          <FamilyInfo />
          <SectionDivider direction="right" />
          <Timeline />
          <SectionDivider direction="left" />
          <LocationMap id="location" />
          <SectionDivider direction="right" />
          <Gallery />
          <SectionDivider direction="left" />
          <GiftMoney id="gift-money" />
          <SectionDivider direction="right" />
          <Guestbook id="guestbook" />
          <SectionDivider direction="left" />
          <RSVP />
        </main>
        <FloatingMenu />
        <BackToTop />
        <Footer />
      </div>
    </div>
  )
}

export default App
