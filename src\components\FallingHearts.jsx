import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

const FallingHearts = () => {
  const [hearts, setHearts] = useState([]);

  const heartSymbols = ['💕', '💖', '💗', '💝', '💘', '💞', '💓', '💟', '❤️', '🧡', '💛', '💚', '💙', '💜', '🤍', '🖤', '🤎'];

  useEffect(() => {
    const createHeart = () => {
      const id = Math.random();
      const left = Math.random() * 100;
      const symbol = heartSymbols[Math.floor(Math.random() * heartSymbols.length)];
      const size = Math.random() * 0.8 + 0.5; // 0.5 to 1.3
      const duration = Math.random() * 4 + 6; // 6 to 10 seconds
      const delay = Math.random() * 2;

      return {
        id,
        left,
        symbol,
        size,
        duration,
        delay
      };
    };

    // Create initial hearts
    const initialHearts = Array.from({ length: 15 }, createHeart);
    setHearts(initialHearts);

    // Add new hearts periodically
    const interval = setInterval(() => {
      setHearts(prevHearts => {
        const newHeart = createHeart();
        // Keep only recent hearts to prevent memory issues
        const filteredHearts = prevHearts.slice(-20);
        return [...filteredHearts, newHeart];
      });
    }, 2000); // Add new heart every 2 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="falling-hearts">
      {hearts.map((heart) => (
        <motion.div
          key={heart.id}
          className="falling-heart"
          style={{
            left: `${heart.left}%`,
            fontSize: `${heart.size * 24}px`,
          }}
          initial={{
            y: -100,
            rotate: 0,
            opacity: 0
          }}
          animate={{
            y: window.innerHeight + 100,
            rotate: 360,
            opacity: [0, 0.8, 0.6, 0]
          }}
          transition={{
            duration: heart.duration,
            delay: heart.delay,
            ease: "linear",
            repeat: Infinity,
            repeatDelay: Math.random() * 5 + 3
          }}
        >
          {heart.symbol}
        </motion.div>
      ))}
      
      {/* Additional static floating hearts for ambiance */}
      <div className="absolute inset-0">
        {Array.from({ length: 8 }).map((_, index) => (
          <motion.div
            key={`static-${index}`}
            className="absolute text-rose-gold/20"
            style={{
              left: `${10 + index * 11}%`,
              top: `${20 + (index % 3) * 25}%`,
              fontSize: '18px'
            }}
            animate={{
              y: [0, -15, 0],
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 4 + index * 0.5,
              repeat: Infinity,
              delay: index * 0.8,
              ease: "easeInOut"
            }}
          >
            💕
          </motion.div>
        ))}
      </div>
      
      {/* Corner decorative hearts */}
      <motion.div
        className="absolute top-10 left-10 text-rose-gold/40 text-2xl"
        animate={{
          scale: [1, 1.2, 1],
          rotate: [0, 15, -15, 0]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        💖
      </motion.div>
      
      <motion.div
        className="absolute top-10 right-10 text-champagne/40 text-2xl"
        animate={{
          scale: [1, 1.3, 1],
          rotate: [0, -15, 15, 0]
        }}
        transition={{
          duration: 3.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      >
        💝
      </motion.div>
      
      <motion.div
        className="absolute bottom-10 left-10 text-sage/40 text-2xl"
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 20, -20, 0]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      >
        💞
      </motion.div>
      
      <motion.div
        className="absolute bottom-10 right-10 text-blush/40 text-2xl"
        animate={{
          scale: [1, 1.25, 1],
          rotate: [0, -20, 20, 0]
        }}
        transition={{
          duration: 3.2,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1.5
        }}
      >
        💗
      </motion.div>
    </div>
  );
};

export default FallingHearts;
