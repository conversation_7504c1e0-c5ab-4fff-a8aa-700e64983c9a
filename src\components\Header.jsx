import { motion } from 'framer-motion';
import { Heart, <PERSON>, MapP<PERSON>, Menu, X } from 'lucide-react';
import { useState } from 'react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-lg border-b border-white/30 shadow-lg"
      style={{
        background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,228,225,0.9) 50%, rgba(247,231,206,0.9) 100%)'
      }}
    >
      <nav className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <motion.div
            className="flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            animate={{
              y: [0, -2, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <Heart className="w-6 h-6 text-rose-gold" />
            </motion.div>
            <span className="font-didot text-xl font-semibold text-gray-800 tracking-wide">
              Thiệp Cưới
            </span>
            <motion.div
              className="text-champagne text-sm"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              ✨
            </motion.div>
          </motion.div>

          <div className="hidden md:flex items-center space-x-8">
            <motion.a
              href="#home"
              className="text-gray-700 hover:text-rose-gold transition-colors"
              whileHover={{ y: -2 }}
            >
              Trang Chủ
            </motion.a>
            <motion.a
              href="#timeline"
              className="text-gray-700 hover:text-rose-gold transition-colors"
              whileHover={{ y: -2 }}
            >
              Lịch Trình
            </motion.a>
            <motion.a
              href="#gallery"
              className="text-gray-700 hover:text-rose-gold transition-colors"
              whileHover={{ y: -2 }}
            >
              Thư Viện
            </motion.a>
            <motion.a
              href="#rsvp"
              className="text-gray-700 hover:text-rose-gold transition-colors"
              whileHover={{ y: -2 }}
            >
              Xác Nhận
            </motion.a>
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-4">
              <motion.div
                className="flex items-center space-x-1 text-sm text-gray-600"
                whileHover={{ scale: 1.05 }}
              >
                <Calendar className="w-4 h-4" />
                <span>15/08/2024</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-1 text-sm text-gray-600"
                whileHover={{ scale: 1.05 }}
              >
                <MapPin className="w-4 h-4" />
                <span>Hà Nội</span>
              </motion.div>
            </div>

            {/* Mobile menu button */}
            <motion.button
              className="md:hidden p-2"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="w-6 h-6 text-gray-700" />
              ) : (
                <Menu className="w-6 h-6 text-gray-700" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden mt-4 pb-4 border-t border-white/20"
          >
            <div className="flex flex-col space-y-4 pt-4">
              <motion.a
                href="#home"
                className="text-gray-700 hover:text-rose-gold transition-colors"
                whileHover={{ x: 5 }}
                onClick={() => setIsMenuOpen(false)}
              >
                Trang Chủ
              </motion.a>
              <motion.a
                href="#timeline"
                className="text-gray-700 hover:text-rose-gold transition-colors"
                whileHover={{ x: 5 }}
                onClick={() => setIsMenuOpen(false)}
              >
                Lịch Trình
              </motion.a>
              <motion.a
                href="#gallery"
                className="text-gray-700 hover:text-rose-gold transition-colors"
                whileHover={{ x: 5 }}
                onClick={() => setIsMenuOpen(false)}
              >
                Thư Viện
              </motion.a>
              <motion.a
                href="#rsvp"
                className="text-gray-700 hover:text-rose-gold transition-colors"
                whileHover={{ x: 5 }}
                onClick={() => setIsMenuOpen(false)}
              >
                Xác Nhận
              </motion.a>

              <div className="flex flex-col space-y-2 pt-4 border-t border-white/20">
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>15/08/2024</span>
                </div>
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span>Hà Nội</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </nav>
    </motion.header>
  );
};

export default Header;
