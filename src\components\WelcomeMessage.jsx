import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const WelcomeMessage = () => {
  const [showMessage, setShowMessage] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowMessage(false);
    }, 4000);

    return () => clearTimeout(timer);
  }, []);

  if (!showMessage) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed inset-0 z-[100] flex items-center justify-center bg-gradient-to-br from-blush/95 via-champagne/95 to-rose-gold/95 backdrop-blur-sm"
    >
      <motion.div
        initial={{ scale: 0.8, y: 50 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.8, y: -50 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="text-center px-8"
      >
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mb-8"
        >
          <h1 className="font-carolyna text-4xl md:text-6xl lg:text-7xl font-bold text-gray-800 mb-4">
            <motion.span
              animate={{
                textShadow: [
                  "0 0 0 rgba(232,180,184,0)",
                  "0 0 20px rgba(232,180,184,0.6)",
                  "0 0 0 rgba(232,180,184,0)"
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              Chào Mừng
            </motion.span>
          </h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="font-didot text-xl md:text-2xl text-gray-700 tracking-wide"
          >
            đến với thiệp cưới của chúng tôi
          </motion.p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          className="flex justify-center space-x-4 text-4xl"
        >
          <motion.span
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0 }}
          >
            💕
          </motion.span>
          <motion.span
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
          >
            🌸
          </motion.span>
          <motion.span
            animate={{ rotate: [0, -10, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 1 }}
          >
            ✨
          </motion.span>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-8"
        >
          <motion.button
            onClick={() => setShowMessage(false)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-3 bg-white/80 backdrop-blur-sm rounded-full font-didot text-gray-800 font-medium tracking-wide shadow-lg hover:shadow-xl transition-all duration-300"
          >
            Bắt Đầu Khám Phá
          </motion.button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 0.8, delay: 2 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-gray-600 text-sm font-didot"
          >
            Nhấn để tiếp tục ↓
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default WelcomeMessage;
