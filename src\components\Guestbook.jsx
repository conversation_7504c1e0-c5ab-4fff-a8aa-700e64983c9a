import { motion } from 'framer-motion';
import { useState } from 'react';
import { Send, Heart, MessageCircle, Star, Copy } from 'lucide-react';

const Guestbook = ({ id }) => {
  const [formData, setFormData] = useState({
    name: '',
    message: ''
  });
  const [messages, setMessages] = useState([
    {
      id: 1,
      name: "<PERSON><PERSON> đình <PERSON>",
      message: "Chúc mừng hạnh phúc! Chúc hai bạn trăm năm hạnh phúc!",
      time: "2 giờ trước"
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON> thân Lan",
      message: "Thật vui vì cuối cùng ngày này cũng tới với bạn. Tôi thành tâm chúc hai bạn thật nhiều hạnh phúc và sống đời vui vẻ cùng nhau mãi mãi!",
      time: "5 giờ trước"
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON> nghiệ<PERSON>",
      message: "<PERSON><PERSON><PERSON> đôi trai tài gái sắc hạnh phúc trọn vẹn, luôn yêu thương nhau thật nhiều!",
      time: "1 ngày trước"
    }
  ]);

  const templateMessages = [
    "Chúc mừng hạnh phúc! Chúc hai bạn trăm năm hạnh phúc!",
    "Chúc mừng ngày trọng đại tới hai bạn. Hạnh phúc bền lâu và trọn vẹn nhé!",
    "Chúc hai bạn bên nhau đầu bạc răng long, sớm có thiên thần nhỏ nhé!",
    "Thật vui vì cuối cùng ngày này cũng tới với bạn. Chúc hai bạn thật nhiều hạnh phúc!",
    "Chúc đôi trai tài gái sắc hạnh phúc trọn vẹn, luôn yêu thương nhau thật nhiều!",
    "Chúc hai bạn có cuộc sống mới ngập tràn tiếng cười và niềm vui!",
    "Mong tình yêu của hai bạn thật bền chặt, gắn bó để xây dựng tổ ấm yên bình!",
    "Chúc hai bạn sức khỏe dồi dào, làm ăn phát đạt và sớm có cháu cho ông bà bồng bế!"
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.name && formData.message) {
      const newMessage = {
        id: messages.length + 1,
        name: formData.name,
        message: formData.message,
        time: "Vừa xong"
      };
      setMessages([newMessage, ...messages]);
      setFormData({ name: '', message: '' });
    }
  };

  const useTemplate = (template) => {
    setFormData({
      ...formData,
      message: template
    });
  };

  return (
    <section id={id} className="py-20">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
            className="text-5xl mb-6"
          >
            📝
          </motion.div>

          <h2 className="font-didot text-5xl font-bold text-gray-800 mb-4 tracking-wide">
            Sổ Lưu Bút
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Hãy để lại những lời chúc tốt đẹp cho chúng tôi trong ngày trọng đại này
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Form Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="wedding-card p-8 relative overflow-hidden">
              {/* Background elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-rose-gold/10 via-champagne/10 to-sage/10 rounded-3xl" />

              <motion.div
                className="absolute top-4 right-4 text-rose-gold/30 text-2xl"
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                ✨
              </motion.div>

              <div className="relative z-10">
                <h3 className="font-didot text-2xl font-bold text-gray-800 mb-6 tracking-wide flex items-center">
                  <MessageCircle className="w-6 h-6 text-rose-gold mr-3" />
                  Gửi Lời Chúc
                </h3>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                  >
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Tên của bạn"
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-rose-gold focus:border-transparent transition-all font-didot"
                    />
                  </motion.div>

                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                  >
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      placeholder="Lời chúc của bạn..."
                      rows="4"
                      required
                      className="w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-rose-gold focus:border-transparent transition-all resize-none font-didot"
                    />
                  </motion.div>

                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-rose-gold text-white py-3 px-6 rounded-2xl font-didot font-medium text-lg shadow-lg hover:bg-rose-gold/90 transition-all duration-300 flex items-center justify-center space-x-2"
                  >
                    <Send className="w-5 h-5" />
                    <span>Gửi Lời Chúc</span>
                  </motion.button>
                </form>

                {/* Template Messages */}
                <div className="mt-8">
                  <h4 className="font-didot text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <Star className="w-5 h-5 text-champagne mr-2" />
                    Gợi ý lời chúc
                  </h4>
                  <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                    {templateMessages.slice(0, 4).map((template, index) => (
                      <motion.button
                        key={index}
                        onClick={() => useTemplate(template)}
                        whileHover={{ scale: 1.02, x: 5 }}
                        className="text-left p-3 bg-white/50 rounded-xl text-sm text-gray-700 hover:bg-white/80 transition-all font-didot border border-gray-100"
                      >
                        "{template}"
                      </motion.button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Messages Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="wedding-card p-8 relative overflow-hidden">
              {/* Background elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-sage/10 via-blush/10 to-champagne/10 rounded-3xl" />

              <motion.div
                className="absolute bottom-4 left-4 text-sage/30 text-2xl"
                animate={{ rotate: [0, -360] }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
              >
                🌸
              </motion.div>

              <div className="relative z-10">
                <h3 className="font-didot text-2xl font-bold text-gray-800 mb-6 tracking-wide flex items-center">
                  <Heart className="w-6 h-6 text-rose-gold mr-3" />
                  Lời Chúc Từ Mọi Người
                </h3>

                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {messages.map((msg, index) => (
                    <motion.div
                      key={msg.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/70 backdrop-blur-sm rounded-2xl p-4 border border-white/30"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-didot font-semibold text-gray-800">
                          {msg.name}
                        </h4>
                        <span className="text-xs text-gray-500 font-didot">
                          {msg.time}
                        </span>
                      </div>
                      <p className="text-gray-700 font-didot leading-relaxed">
                        {msg.message}
                      </p>

                      <motion.div
                        className="flex items-center mt-3 space-x-2"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.3 }}
                      >
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="text-rose-gold hover:text-rose-gold/80 transition-colors"
                        >
                          <Heart className="w-4 h-4" />
                        </motion.button>
                        <span className="text-xs text-gray-500 font-didot">
                          Cảm ơn bạn ❤️
                        </span>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>

                {/* Stats */}
                <motion.div
                  className="mt-6 text-center"
                  animate={{ scale: [1, 1.02, 1] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <div className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30">
                    <p className="font-didot text-sm text-gray-600">
                      <span className="font-semibold text-rose-gold">{messages.length}</span> lời chúc đã được gửi
                    </p>
                    <div className="flex justify-center space-x-1 mt-2 text-lg">
                      <span>💕</span>
                      <span>🌸</span>
                      <span>✨</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Guestbook;
