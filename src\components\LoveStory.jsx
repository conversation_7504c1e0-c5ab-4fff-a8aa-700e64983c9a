import { motion } from 'framer-motion';
import { Heart, Calendar, MapPin, Star } from 'lucide-react';

const LoveStory = () => {
  const storyMilestones = [
    {
      date: "Tháng 3, 2020",
      title: "Lần Đầu Gặp Gỡ",
      description: "Chúng tôi gặp nhau lần đầu tại một quán cà phê nhỏ ở Hà Nội. Đó là một buổi chiều mùa xuân đẹp trời, và từ cái nhìn đầu tiên, chúng tôi đã biết rằng đây là định mệnh.",
      image: "https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400&h=300&fit=crop",
      icon: "💕",
      color: "from-rose-gold to-champagne"
    },
    {
      date: "Tháng 8, 2020",
      title: "Tình Yêu Nảy Nở",
      description: "<PERSON>u những tháng ngày tìm hiểu, chúng tôi ch<PERSON>h thức trở thành người yêu của nhau. Tình yêu của chúng tôi như những bông hoa nở rộ trong mùa hè.",
      image: "https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?w=400&h=300&fit=crop",
      icon: "🌸",
      color: "from-champagne to-blush"
    },
    {
      date: "Tháng 12, 2022",
      title: "Lời Cầu Hôn",
      description: "Trong một buổi tối Giáng sinh lãng mạn, anh đã quỳ gối cầu hôn em. Đó là khoảnh khắc đẹp nhất trong cuộc đời chúng tôi, khi em nói 'Có' với đôi mắt đầy nước mắt hạnh phúc.",
      image: "https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=300&fit=crop",
      icon: "💍",
      color: "from-blush to-sage"
    },
    {
      date: "Tháng 8, 2024",
      title: "Ngày Cưới",
      description: "Và cuối cùng, ngày trọng đại đã đến! Chúng tôi sẽ chính thức trở thành vợ chồng, bắt đầu hành trình mới cùng nhau với tình yêu và sự chúc phước từ gia đình, bạn bè.",
      image: "https://images.unsplash.com/photo-1606216794074-735e91aa2c92?w=400&h=300&fit=crop",
      icon: "👰‍♀️",
      color: "from-sage to-rose-gold"
    }
  ];

  return (
    <section className="py-20 bg-white/20">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
            className="text-5xl mb-6"
          >
            💕
          </motion.div>
          
          <h2 className="font-didot text-5xl font-bold text-gray-800 mb-4 tracking-wide">
            Chuyện Tình Yêu Của Chúng Tôi
          </h2>
          <p className="text-gray-600 text-lg max-w-3xl mx-auto leading-relaxed">
            Mỗi câu chuyện tình yêu đều có những khoảnh khắc đặc biệt. 
            Đây là hành trình tình yêu của chúng tôi, từ những ngày đầu gặp gỡ cho đến ngày trọng đại.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          {storyMilestones.map((milestone, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={`flex flex-col lg:flex-row items-center mb-20 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Image */}
              <motion.div
                className="w-full lg:w-1/2 mb-8 lg:mb-0"
                whileHover={{ scale: 1.02 }}
              >
                <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                  <motion.img
                    src={milestone.image}
                    alt={milestone.title}
                    className="w-full h-80 object-cover"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  />
                  
                  {/* Overlay with icon */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <motion.div
                    className="absolute top-4 right-4 text-4xl"
                    animate={{ 
                      rotate: [0, 10, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ duration: 4, repeat: Infinity }}
                  >
                    {milestone.icon}
                  </motion.div>
                  
                  {/* Date badge */}
                  <motion.div
                    className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-2xl px-4 py-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-rose-gold" />
                      <span className="font-didot text-sm font-medium text-gray-800">
                        {milestone.date}
                      </span>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Content */}
              <div className={`w-full lg:w-1/2 ${index % 2 === 1 ? 'lg:pr-12' : 'lg:pl-12'}`}>
                <motion.div
                  className="wedding-card p-8 relative overflow-hidden"
                  whileHover={{ y: -5 }}
                >
                  {/* Background gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${milestone.color} opacity-10 rounded-3xl`} />
                  
                  {/* Decorative elements */}
                  <motion.div
                    className="absolute top-4 right-4 text-rose-gold/30 text-2xl"
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  >
                    ✨
                  </motion.div>

                  <div className="relative z-10">
                    <motion.h3
                      className="font-didot text-3xl font-bold text-gray-800 mb-4 tracking-wide"
                      whileHover={{ scale: 1.02 }}
                    >
                      {milestone.title}
                    </motion.h3>
                    
                    <motion.p
                      className="text-gray-700 font-didot leading-relaxed text-lg"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      transition={{ duration: 0.8, delay: 0.3 }}
                      viewport={{ once: true }}
                    >
                      {milestone.description}
                    </motion.p>

                    {/* Decorative divider */}
                    <motion.div
                      className="flex items-center mt-6"
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 3, repeat: Infinity }}
                    >
                      <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                      <span className="mx-3 text-champagne text-lg">💖</span>
                      <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="wedding-card max-w-4xl mx-auto p-8">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-4xl mb-6"
            >
              💑
            </motion.div>
            
            <h3 className="font-didot text-3xl font-bold text-gray-800 mb-6 tracking-wide">
              "Tình yêu không chỉ là nhìn vào mắt nhau, mà là cùng nhau nhìn về một hướng"
            </h3>
            <p className="text-gray-600 font-didot leading-relaxed text-lg mb-6">
              Chúng tôi đã tìm thấy nhau và giờ đây sẵn sàng bước vào hành trình mới. 
              Cảm ơn bạn đã là một phần trong câu chuyện tình yêu của chúng tôi.
            </p>
            
            <motion.div
              className="flex justify-center space-x-4 text-3xl"
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span>💕</span>
              <span>🌸</span>
              <span>💍</span>
              <span>👰‍♀️</span>
              <span>🤵‍♂️</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default LoveStory;
