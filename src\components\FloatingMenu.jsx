import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { MessageCircle, Gift, CreditCard, Menu, X, Heart, Calendar, MapPin } from 'lucide-react';

const FloatingMenu = () => {
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = [
    {
      icon: <MessageCircle className="w-5 h-5" />,
      label: "Gửi lời chúc",
      action: () => document.getElementById('guestbook')?.scrollIntoView({ behavior: 'smooth' }),
      color: "bg-rose-gold",
      emoji: "💌"
    },
    {
      icon: <Calendar className="w-5 h-5" />,
      label: "Xác nhận tham dự",
      action: () => document.getElementById('rsvp')?.scrollIntoView({ behavior: 'smooth' }),
      color: "bg-sage",
      emoji: "📅"
    },
    {
      icon: <CreditCard className="w-5 h-5" />,
      label: "<PERSON><PERSON><PERSON> cưới",
      action: () => document.getElementById('gift-money')?.scrollIntoView({ behavior: 'smooth' }),
      color: "bg-champagne",
      emoji: "🎁"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      label: "Địa điểm",
      action: () => document.getElementById('location')?.scrollIntoView({ behavior: 'smooth' }),
      color: "bg-blush",
      emoji: "📍"
    }
  ];

  return (
    <div className="fixed bottom-6 left-6 z-50">
      {/* Menu Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="mb-4 space-y-3"
          >
            {menuItems.map((item, index) => (
              <motion.button
                key={index}
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                onClick={item.action}
                whileHover={{ scale: 1.1, x: 10 }}
                whileTap={{ scale: 0.9 }}
                className={`${item.color} text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-3 min-w-max group`}
              >
                <span className="text-xl">{item.emoji}</span>
                <span className="font-didot font-medium text-sm whitespace-nowrap pr-2">
                  {item.label}
                </span>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        className="w-14 h-14 bg-rose-gold text-white rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden"
        animate={{
          rotate: isOpen ? 180 : 0,
        }}
        transition={{ duration: 0.3 }}
      >
        {/* Background pulse effect */}
        <motion.div
          className="absolute inset-0 bg-rose-gold rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.8, 0.4, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
          }}
        />
        
        {/* Icon */}
        <div className="relative z-10">
          {isOpen ? (
            <X className="w-6 h-6" />
          ) : (
            <Menu className="w-6 h-6" />
          )}
        </div>

        {/* Floating hearts when closed */}
        {!isOpen && (
          <motion.div
            className="absolute -top-1 -right-1 text-xs"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 10, -10, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
            }}
          >
            💕
          </motion.div>
        )}
      </motion.button>

      {/* Notification Badge */}
      {!isOpen && (
        <motion.div
          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold"
          animate={{
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
          }}
        >
          4
        </motion.div>
      )}

      {/* Helper Text */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3, delay: 1 }}
            className="absolute left-16 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/30 pointer-events-none"
          >
            <p className="font-didot text-sm text-gray-800 whitespace-nowrap">
              Menu tiện ích
            </p>
            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-white/90 rotate-45 border-l border-b border-white/30" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingMenu;
