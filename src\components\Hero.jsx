import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Sparkles, X } from 'lucide-react';
import { useState } from 'react';

const Hero = () => {
  const [isInvitationOpen, setIsInvitationOpen] = useState(false);
  const floatingHearts = Array.from({ length: 9 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute text-rose-gold/30"
      style={{
        left: `${10 + i * 10}%`,
        top: `${Math.random() * 80 + 10}%`,
      }}
      animate={{
        y: [0, -20, 0],
        rotate: [0, 10, -10, 0],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        repeat: Infinity,
        delay: i * 0.5,
      }}
    >
      <Heart className="w-4 h-4" />
    </motion.div>
  ));

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-gradient-to-br from-blush/90 via-champagne/90 to-rose-gold/90" />
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-rose-gold/5 animate-pulse" />
        </div>
      </div>
      {/* Floating Hearts Background */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingHearts}
      </div>

      {/* Sparkles Animation */}
      <motion.div
        className="absolute top-20 right-20 text-champagne"
        animate={{
          rotate: 360,
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>

      <motion.div
        className="absolute bottom-20 left-20 text-sage"
        animate={{
          rotate: -360,
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-6 h-6" />
      </motion.div>

      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Save the Date Badge */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <div className="inline-flex items-center bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/30">
            <span className="font-didot text-rose-gold font-medium tracking-wider text-sm">
              SAVE THE DATE
            </span>
          </div>
        </motion.div>

        {/* Wedding Invitation Card - Clickable */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="wedding-card max-w-5xl mx-auto p-8 sm:p-12 md:p-16 relative overflow-hidden cursor-pointer group"
          onClick={() => setIsInvitationOpen(true)}
          whileHover={{
            scale: 1.02,
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
            y: -5
          }}
          whileTap={{ scale: 0.98 }}
        >
          {/* Decorative corner flourishes */}
          <div className="absolute top-0 left-0 w-20 h-20 opacity-20">
            <svg viewBox="0 0 100 100" className="w-full h-full text-rose-gold">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute top-0 right-0 w-20 h-20 opacity-20 transform rotate-90">
            <svg viewBox="0 0 100 100" className="w-full h-full text-champagne">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute bottom-0 left-0 w-20 h-20 opacity-20 transform rotate-180">
            <svg viewBox="0 0 100 100" className="w-full h-full text-sage">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          <div className="absolute bottom-0 right-0 w-20 h-20 opacity-20 transform rotate-270">
            <svg viewBox="0 0 100 100" className="w-full h-full text-blush">
              <path d="M20,20 Q50,5 80,20 Q95,50 80,80 Q50,95 20,80 Q5,50 20,20" fill="currentColor" />
            </svg>
          </div>
          {/* Wedding Announcement */}
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-sage font-playfair font-medium text-xl mb-6 tracking-wider"
          >
            CHÚNG TÔI SẮP KẾT HÔN
          </motion.p>

          {/* Couple Names */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="mb-8"
          >
            <div className="relative">
              <h1 className="font-great-vibes text-5xl sm:text-7xl md:text-8xl lg:text-9xl font-normal text-gray-800 mb-6 relative">
                <motion.span
                  className="inline-block relative"
                  whileHover={{
                    scale: 1.05,
                    color: "#E8B4B8",
                    textShadow: "0 0 20px rgba(232, 180, 184, 0.6)"
                  }}
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  Minh
                  <motion.div
                    className="absolute -top-2 -right-2 text-rose-gold text-lg"
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    ✨
                  </motion.div>
                </motion.span>

                <motion.span
                  className="inline-block mx-2 sm:mx-4 md:mx-6 text-rose-gold relative"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  &
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-rose-gold to-champagne opacity-20 rounded-full blur-lg"
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </motion.span>

                <motion.span
                  className="inline-block relative"
                  whileHover={{
                    scale: 1.05,
                    color: "#E8B4B8",
                    textShadow: "0 0 20px rgba(232, 180, 184, 0.6)"
                  }}
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1.5
                  }}
                >
                  Hương
                  <motion.div
                    className="absolute -top-2 -left-2 text-sage text-lg"
                    animate={{ rotate: [0, -10, 10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                  >
                    💖
                  </motion.div>
                </motion.span>
              </h1>

              {/* Decorative elements */}
              <motion.div
                className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-champagne text-2xl"
                animate={{
                  y: [0, -10, 0],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                ✨ ✨ ✨
              </motion.div>

              <motion.div
                className="absolute -bottom-4 left-1/4 text-rose-gold text-xl"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 8, repeat: Infinity }}
              >
                🌹
              </motion.div>

              <motion.div
                className="absolute -bottom-4 right-1/4 text-sage text-xl"
                animate={{
                  rotate: [360, 0],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 8, repeat: Infinity, delay: 2 }}
              >
                🌿
              </motion.div>
            </div>
          </motion.div>

          {/* Wedding Date */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="mb-8"
          >
            <p className="text-gray-600 font-playfair text-xl mb-2">Ngày cưới</p>
            <p className="font-dancing text-3xl sm:text-4xl md:text-5xl font-semibold text-gray-800">
              15 tháng 8, 2024
            </p>
          </motion.div>

          {/* Location */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="mb-10"
          >
            <p className="text-gray-600 font-playfair text-lg">
              Trung tâm Hội nghị Quốc gia, Hà Nội
            </p>
          </motion.div>

          {/* Click to Open Invitation */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.7 }}
            className="text-center"
          >
            <div className="bg-rose-gold/10 text-rose-gold px-6 py-3 rounded-full font-playfair font-medium text-lg shadow-lg border-2 border-rose-gold/30 group-hover:bg-rose-gold/20 transition-all duration-300">
              <span className="flex items-center justify-center gap-2">
                <Heart className="w-5 h-5" />
                Nhấp vào để mở thiệp
                <Heart className="w-5 h-5" />
              </span>
            </div>
            <p className="text-gray-500 text-sm mt-2 font-playfair">Click to open invitation</p>
          </motion.div>
        </motion.div>
      </div>

      {/* Full Screen Invitation Modal */}
      <AnimatePresence>
        {isInvitationOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setIsInvitationOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.5, opacity: 0, rotateY: -90 }}
              animate={{ scale: 1, opacity: 1, rotateY: 0 }}
              exit={{ scale: 0.5, opacity: 0, rotateY: 90 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="bg-gradient-to-br from-champagne via-white to-blush max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => setIsInvitationOpen(false)}
                className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-300"
              >
                <X className="w-6 h-6 text-gray-600" />
              </button>

              {/* Invitation Content */}
              <div className="p-8 sm:p-12 md:p-16 text-center relative overflow-hidden">
                {/* Decorative Background */}
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute top-10 left-10 text-rose-gold text-6xl">🌹</div>
                  <div className="absolute top-20 right-20 text-champagne text-4xl">✨</div>
                  <div className="absolute bottom-20 left-20 text-sage text-5xl">🌿</div>
                  <div className="absolute bottom-10 right-10 text-blush text-6xl">💕</div>
                </div>

                {/* Header */}
                <motion.div
                  initial={{ y: -30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="mb-8"
                >
                  <h2 className="font-dancing text-2xl text-rose-gold mb-4">Wedding Invitation</h2>
                  <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-rose-gold to-transparent mx-auto"></div>
                </motion.div>

                {/* Main Content */}
                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="mb-8"
                >
                  <p className="font-playfair text-gray-600 text-lg mb-6">
                    Với niềm hạnh phúc và vinh dự, chúng tôi xin kính mời
                  </p>

                  <div className="bg-white/60 rounded-xl p-8 mb-8 shadow-lg">
                    <h1 className="font-great-vibes text-6xl md:text-7xl text-gray-800 mb-4">
                      Minh & Hương
                    </h1>
                    <p className="font-dancing text-3xl text-rose-gold mb-4">
                      15 tháng 8, 2024
                    </p>
                    <p className="font-playfair text-gray-600 text-lg">
                      Trung tâm Hội nghị Quốc gia, Hà Nội
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8 mb-8">
                    <div className="bg-white/40 rounded-xl p-6">
                      <h3 className="font-dancing text-2xl text-rose-gold mb-3">Lễ Vu Quy</h3>
                      <p className="font-playfair text-gray-600">
                        <strong>Thời gian:</strong> 09:00 AM<br/>
                        <strong>Địa điểm:</strong> Nhà gái<br/>
                        123 Đường ABC, Hà Nội
                      </p>
                    </div>
                    <div className="bg-white/40 rounded-xl p-6">
                      <h3 className="font-dancing text-2xl text-rose-gold mb-3">Tiệc Cưới</h3>
                      <p className="font-playfair text-gray-600">
                        <strong>Thời gian:</strong> 18:00 PM<br/>
                        <strong>Địa điểm:</strong> Trung tâm Hội nghị<br/>
                        Quốc gia, Hà Nội
                      </p>
                    </div>
                  </div>

                  <p className="font-playfair text-gray-600 text-lg italic mb-8">
                    "Sự hiện diện của bạn sẽ là niềm hạnh phúc lớn nhất của chúng tôi trong ngày trọng đại này"
                  </p>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-rose-gold text-white px-8 py-4 rounded-full font-playfair font-medium text-lg shadow-lg hover:bg-rose-gold/90 transition-all duration-300"
                    onClick={() => {
                      setIsInvitationOpen(false);
                      document.getElementById('rsvp').scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Xác Nhận Tham Dự
                  </motion.button>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default Hero;
