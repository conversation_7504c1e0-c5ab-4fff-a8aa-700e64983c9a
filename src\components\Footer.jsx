import { motion } from 'framer-motion';
import { Heart, MapPin, Phone, Mail, Calendar } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white py-16">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="font-serif text-2xl font-semibold mb-6 flex items-center">
              <Heart className="w-6 h-6 text-rose-gold mr-2" />
              Li<PERSON>n <PERSON>
            </h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-rose-gold mr-3" />
                <div>
                  <p className="font-medium">Chú Rể: Minh</p>
                  <p className="text-gray-300">0123 456 789</p>
                </div>
              </div>
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-rose-gold mr-3" />
                <div>
                  <p className="font-medium">Cô Dâu: Hương</p>
                  <p className="text-gray-300">0987 654 321</p>
                </div>
              </div>
              <div className="flex items-center">
                <Mail className="w-5 h-5 text-rose-gold mr-3" />
                <p className="text-gray-300"><EMAIL></p>
              </div>
            </div>
          </motion.div>

          {/* Wedding Details */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="font-serif text-2xl font-semibold mb-6 flex items-center">
              <Calendar className="w-6 h-6 text-rose-gold mr-2" />
              Chi Tiết Cưới
            </h3>
            <div className="space-y-4">
              <div>
                <p className="font-medium text-rose-gold">Ngày Cưới</p>
                <p className="text-gray-300">Thứ Bảy, 15 tháng 8, 2024</p>
              </div>
              <div>
                <p className="font-medium text-rose-gold">Thời Gian</p>
                <p className="text-gray-300">18:00 - 21:00</p>
              </div>
              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-rose-gold mr-3 mt-1" />
                <div>
                  <p className="font-medium">Địa Điểm</p>
                  <p className="text-gray-300">
                    Trung tâm Hội nghị Quốc gia<br />
                    Số 1 Thăng Long, Ba Đình, Hà Nội
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Thank You Message */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="font-serif text-2xl font-semibold mb-6">
              Lời Cảm Ơn
            </h3>
            <p className="text-gray-300 leading-relaxed mb-6">
              Cảm ơn tất cả gia đình, bạn bè đã dành thời gian quý báu để tham dự 
              ngày trọng đại của chúng tôi. Sự hiện diện của các bạn sẽ làm cho 
              ngày cưới của chúng tôi trở nên ý nghĩa và đáng nhớ hơn.
            </p>
            <motion.div
              className="flex items-center justify-center space-x-2 text-rose-gold"
              animate={{
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              <Heart className="w-5 h-5" />
              <span className="font-serif text-lg">Minh & Hương</span>
              <Heart className="w-5 h-5" />
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="border-t border-gray-700 mt-12 pt-8 text-center"
        >
          <p className="text-gray-400">
            © 2024 Minh & Hương Wedding. Made with{' '}
            <Heart className="w-4 h-4 inline text-rose-gold" /> for our special day.
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
