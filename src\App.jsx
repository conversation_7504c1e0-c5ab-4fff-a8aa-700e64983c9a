import Header from './components/Header'
import Hero from './components/Hero'
import Timeline from './components/Timeline'
import Gallery from './components/Gallery'
import RSVP from './components/RSVP'
import Footer from './components/Footer'
import BackgroundAnimations from './components/BackgroundAnimations'
import { EnhancedAnimations, SectionDivider } from './components/EnhancedAnimations'
import WelcomeMessage from './components/WelcomeMessage'

function App() {
  return (
    <div className="min-h-screen relative">
      <WelcomeMessage />
      <BackgroundAnimations />
      <EnhancedAnimations />
      <div className="relative z-10">
        <Header />
        <main>
          <Hero />
          <SectionDivider direction="right" />
          <Timeline />
          <SectionDivider direction="left" />
          <Gallery />
          <SectionDivider direction="right" />
          <RSVP />
        </main>
        <Footer />
      </div>
    </div>
  )
}

export default App
