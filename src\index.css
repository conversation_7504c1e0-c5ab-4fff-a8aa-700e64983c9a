@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    border-color: theme('colors.gray.200');
  }
  body {
    @apply bg-gradient-to-br from-blush via-champagne to-rose-gold font-sans;
    margin: 0;
    min-height: 100vh;
  }
}

@layer components {
  .wedding-card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20;
  }

  .elegant-text {
    @apply font-serif text-gray-800;
  }

  .floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
  }

  .heart {
    position: absolute;
    color: #E8B4B8;
    animation: float 6s ease-in-out infinite;
  }

  .heart:nth-child(1) { left: 10%; animation-delay: 0s; }
  .heart:nth-child(2) { left: 20%; animation-delay: 1s; }
  .heart:nth-child(3) { left: 30%; animation-delay: 2s; }
  .heart:nth-child(4) { left: 40%; animation-delay: 3s; }
  .heart:nth-child(5) { left: 50%; animation-delay: 4s; }
  .heart:nth-child(6) { left: 60%; animation-delay: 5s; }
  .heart:nth-child(7) { left: 70%; animation-delay: 6s; }
  .heart:nth-child(8) { left: 80%; animation-delay: 7s; }
  .heart:nth-child(9) { left: 90%; animation-delay: 8s; }
}
