@import url('https://fonts.googleapis.com/css2?family=Didot:wght@400;500;600;700&display=swap');
@import url('https://fonts.cdnfonts.com/css/carolyna-black');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    border-color: theme('colors.gray.200');
  }
  body {
    @apply bg-gradient-to-br from-blush via-champagne to-rose-gold font-didot;
    margin: 0;
    min-height: 100vh;
    letter-spacing: 0.025em;
  }
}

@layer components {
  .wedding-card {
    @apply bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,228,225,0.9) 50%, rgba(247,231,206,0.95) 100%);
    box-shadow:
      0 25px 50px rgba(0,0,0,0.1),
      0 0 0 1px rgba(255,255,255,0.5) inset,
      0 2px 4px rgba(232,180,184,0.2);
  }

  .elegant-text {
    @apply font-didot text-gray-800 tracking-wide;
  }

  .floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
  }

  .heart {
    position: absolute;
    color: #E8B4B8;
    animation: float 6s ease-in-out infinite;
  }

  .heart:nth-child(1) { left: 10%; animation-delay: 0s; }
  .heart:nth-child(2) { left: 20%; animation-delay: 1s; }
  .heart:nth-child(3) { left: 30%; animation-delay: 2s; }
  .heart:nth-child(4) { left: 40%; animation-delay: 3s; }
  .heart:nth-child(5) { left: 50%; animation-delay: 4s; }
  .heart:nth-child(6) { left: 60%; animation-delay: 5s; }
  .heart:nth-child(7) { left: 70%; animation-delay: 6s; }
  .heart:nth-child(8) { left: 80%; animation-delay: 7s; }
  .heart:nth-child(9) { left: 90%; animation-delay: 8s; }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(247,231,206,0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #E8B4B8, #F7E7CE);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #E8B4B8, #9CAF88);
  }

  /* Text selection */
  ::selection {
    background: rgba(232,180,184,0.3);
    color: #374151;
  }

  /* Smooth focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-rose-gold focus:ring-opacity-50;
  }

  /* Glass morphism effect */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #E8B4B8 0%, #F7E7CE 50%, #9CAF88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 3D Card Effects */
  .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  /* Smooth transitions */
  * {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  /* Enhanced button styles */
  .btn-primary {
    background: linear-gradient(135deg, #E8B4B8 0%, #F7E7CE 100%);
    box-shadow: 0 4px 15px rgba(232, 180, 184, 0.3);
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(232, 180, 184, 0.4);
  }

  /* Loading animation */
  @keyframes shimmer-loading {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer-loading 1.5s infinite;
  }
}
