import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const CountdownTimer = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const weddingDate = new Date('2024-08-15T18:00:00').getTime();

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const difference = weddingDate - now;

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const timeUnits = [
    { label: 'Ngày', value: timeLeft.days, color: 'from-rose-gold to-champagne' },
    { label: 'Giờ', value: timeLeft.hours, color: 'from-champagne to-blush' },
    { label: 'Phút', value: timeLeft.minutes, color: 'from-blush to-sage' },
    { label: 'Giây', value: timeLeft.seconds, color: 'from-sage to-rose-gold' }
  ];

  return (
    <section className="section-spacing-small bg-white/30">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-dancing text-6xl font-bold text-gray-800 mb-4 tracking-wide">
            Đếm Ngược Đến Ngày Cưới
          </h2>
          <p className="text-gray-600 font-playfair text-lg max-w-2xl mx-auto">
            Thời gian còn lại cho đến khi chúng tôi chính thức trở thành vợ chồng
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          {timeUnits.map((unit, index) => (
            <motion.div
              key={unit.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="relative"
            >
              <div className={`wedding-card p-6 text-center relative overflow-hidden`}>
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${unit.color} opacity-10 rounded-3xl`} />

                {/* Decorative elements */}
                <motion.div
                  className="absolute top-2 right-2 text-rose-gold/30 text-sm"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  ✨
                </motion.div>

                <div className="relative z-10">
                  <motion.div
                    key={unit.value}
                    initial={{ scale: 1.2, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="font-carolyna text-4xl md:text-5xl font-bold text-gray-800 mb-2"
                  >
                    {unit.value.toString().padStart(2, '0')}
                  </motion.div>

                  <div className="font-didot text-lg font-medium text-gray-600 tracking-wide">
                    {unit.label}
                  </div>
                </div>

                {/* Pulse effect */}
                <motion.div
                  className="absolute inset-0 border-2 border-rose-gold/20 rounded-3xl"
                  animate={{
                    scale: [1, 1.05, 1],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: index * 0.5
                  }}
                />
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="wedding-card max-w-2xl mx-auto p-8">
            <motion.div
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="mb-4"
            >
              <span className="text-4xl">💕</span>
            </motion.div>

            <h3 className="font-didot text-2xl font-bold text-gray-800 mb-2 tracking-wide">
              Sắp Đến Ngày Trọng Đại!
            </h3>
            <p className="text-gray-600 font-didot">
              Chúng tôi rất mong được chia sẻ niềm hạnh phúc này cùng với bạn
            </p>

            <motion.div
              className="flex justify-center space-x-2 mt-4 text-2xl"
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span>🌸</span>
              <span>💖</span>
              <span>🌿</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CountdownTimer;
