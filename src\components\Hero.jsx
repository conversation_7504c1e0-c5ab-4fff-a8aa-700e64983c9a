import { motion } from 'framer-motion';
import { Heart, Sparkles } from 'lucide-react';

const Hero = () => {
  const floatingHearts = Array.from({ length: 9 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute text-rose-gold/30"
      style={{
        left: `${10 + i * 10}%`,
        top: `${Math.random() * 80 + 10}%`,
      }}
      animate={{
        y: [0, -20, 0],
        rotate: [0, 10, -10, 0],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        repeat: Infinity,
        delay: i * 0.5,
      }}
    >
      <Heart className="w-4 h-4" />
    </motion.div>
  ));

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Floating Hearts Background */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingHearts}
      </div>

      {/* Sparkles Animation */}
      <motion.div
        className="absolute top-20 right-20 text-champagne"
        animate={{
          rotate: 360,
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>

      <motion.div
        className="absolute bottom-20 left-20 text-sage"
        animate={{
          rotate: -360,
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-6 h-6" />
      </motion.div>

      <div className="container mx-auto px-6 text-center relative z-10">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="wedding-card max-w-4xl mx-auto p-6 sm:p-8 md:p-12"
        >
          {/* Wedding Announcement */}
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-sage font-medium text-lg mb-6 tracking-wider"
          >
            CHÚNG TÔI SẮP KẾT HÔN
          </motion.p>

          {/* Couple Names */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="mb-8"
          >
            <h1 className="font-serif text-4xl sm:text-6xl md:text-7xl lg:text-8xl font-bold text-gray-800 mb-4">
              <motion.span
                className="inline-block"
                whileHover={{ scale: 1.05, color: "#E8B4B8" }}
                transition={{ duration: 0.3 }}
              >
                Minh
              </motion.span>
              <motion.span
                className="inline-block mx-2 sm:mx-4 md:mx-6 text-rose-gold"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                &
              </motion.span>
              <motion.span
                className="inline-block"
                whileHover={{ scale: 1.05, color: "#E8B4B8" }}
                transition={{ duration: 0.3 }}
              >
                Hương
              </motion.span>
            </h1>
          </motion.div>

          {/* Wedding Date */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="mb-8"
          >
            <p className="text-gray-600 text-xl mb-2">Ngày cưới</p>
            <p className="font-serif text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-800">
              15 tháng 8, 2024
            </p>
          </motion.div>

          {/* Location */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="mb-10"
          >
            <p className="text-gray-600 text-lg">
              Trung tâm Hội nghị Quốc gia, Hà Nội
            </p>
          </motion.div>

          {/* CTA Button */}
          <motion.button
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.7 }}
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 30px rgba(232, 180, 184, 0.3)"
            }}
            whileTap={{ scale: 0.95 }}
            className="bg-rose-gold text-white px-8 py-4 rounded-full font-medium text-lg shadow-lg hover:bg-rose-gold/90 transition-all duration-300"
            onClick={() => document.getElementById('rsvp').scrollIntoView({ behavior: 'smooth' })}
          >
            Xác Nhận Tham Dự
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
