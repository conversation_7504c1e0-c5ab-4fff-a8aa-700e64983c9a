import Header from './components/Header'
import Hero from './components/Hero'
import InteractiveCard from './components/InteractiveCard'
import CountdownTimer from './components/CountdownTimer'
import FamilyInfo from './components/FamilyInfo'
import Timeline from './components/Timeline'
import LocationMap from './components/LocationMap'
import Gallery from './components/Gallery'
import GiftMoney from './components/GiftMoney'
import RSVP from './components/RSVP'
import Footer from './components/Footer'
import BackgroundAnimations from './components/BackgroundAnimations'
import { EnhancedAnimations, SectionDivider } from './components/EnhancedAnimations'
import WelcomeMessage from './components/WelcomeMessage'
import MusicPlayer from './components/MusicPlayer'

function App() {
  return (
    <div className="min-h-screen relative">
      <WelcomeMessage />
      <BackgroundAnimations />
      <EnhancedAnimations />
      <MusicPlayer />
      <div className="relative z-10">
        <Header />
        <main>
          <Hero />
          <SectionDivider direction="right" />
          <InteractiveCard />
          <SectionDivider direction="left" />
          <CountdownTimer />
          <SectionDivider direction="right" />
          <FamilyInfo />
          <SectionDivider direction="left" />
          <Timeline />
          <SectionDivider direction="right" />
          <LocationMap />
          <SectionDivider direction="left" />
          <Gallery />
          <SectionDivider direction="right" />
          <GiftMoney />
          <SectionDivider direction="left" />
          <RSVP />
        </main>
        <Footer />
      </div>
    </div>
  )
}

export default App
