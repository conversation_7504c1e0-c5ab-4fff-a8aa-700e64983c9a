import { motion } from 'framer-motion';
import { MapPin, Navigation, Clock, Phone, ExternalLink } from 'lucide-react';

const LocationMap = ({ id }) => {
  const venues = [
    {
      title: "<PERSON><PERSON>",
      time: "14:00 - 16:00",
      date: "<PERSON><PERSON><PERSON>, 15/08/2024",
      location: "Nhà Chú Rể",
      address: "456 Láng Hạ, Đống Đa, Hà Nội",
      phone: "0123 456 789",
      mapUrl: "https://maps.google.com/?q=456+<PERSON>+<PERSON>+<PERSON>+<PERSON>+<PERSON>",
      color: "from-rose-gold to-champagne",
      icon: "💒"
    },
    {
      title: "<PERSON><PERSON><PERSON><PERSON> Cưới",
      time: "18:00 - 21:00",
      date: "<PERSON><PERSON><PERSON> Bảy, 15/08/2024",
      location: "Trung tâm Hội nghị Quốc gia",
      address: "Số 1 Thăng Long, Ba Đình, Hà Nội",
      phone: "024 3846 3333",
      mapUrl: "https://maps.google.com/?q=Trung+tam+Ho<PERSON>+nghi+<PERSON>uo<PERSON>+gia+<PERSON><PERSON>",
      color: "from-sage to-blush",
      icon: "🎉"
    }
  ];

  return (
    <section id={id} className="py-20">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-didot text-5xl font-bold text-gray-800 mb-4 tracking-wide">
            Địa Điểm Tổ Chức
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Thông tin chi tiết về địa điểm và thời gian các sự kiện
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {venues.map((venue, index) => (
            <motion.div
              key={venue.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="relative"
            >
              <div className="wedding-card p-8 relative overflow-hidden">
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${venue.color} opacity-10 rounded-3xl`} />

                {/* Decorative elements */}
                <motion.div
                  className="absolute top-4 right-4 text-3xl"
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                >
                  {venue.icon}
                </motion.div>

                <div className="relative z-10">
                  {/* Header */}
                  <motion.div
                    className="text-center mb-6"
                    whileHover={{ scale: 1.02 }}
                  >
                    <h3 className="font-didot text-3xl font-bold text-gray-800 mb-2 tracking-wide">
                      {venue.title}
                    </h3>
                    <div className="flex items-center justify-center text-rose-gold">
                      <Clock className="w-5 h-5 mr-2" />
                      <span className="font-didot text-lg font-medium">
                        {venue.time}
                      </span>
                    </div>
                    <p className="text-gray-600 font-didot mt-1">
                      {venue.date}
                    </p>
                  </motion.div>

                  {/* Location Info */}
                  <div className="space-y-4 mb-6">
                    <motion.div
                      className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-start">
                        <MapPin className="w-5 h-5 text-rose-gold mr-3 mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-didot text-lg font-semibold text-gray-800 mb-1">
                            {venue.location}
                          </h4>
                          <p className="text-gray-600 font-didot text-sm leading-relaxed">
                            {venue.address}
                          </p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center">
                        <Phone className="w-5 h-5 text-sage mr-3" />
                        <div>
                          <span className="font-didot text-sm text-gray-600 mr-2">
                            Liên hệ:
                          </span>
                          <a
                            href={`tel:${venue.phone}`}
                            className="font-didot text-gray-800 font-medium hover:text-rose-gold transition-colors"
                          >
                            {venue.phone}
                          </a>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <motion.a
                      href={venue.mapUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-rose-gold text-white py-3 px-6 rounded-2xl font-didot font-medium text-center flex items-center justify-center space-x-2 hover:bg-rose-gold/90 transition-all duration-300 shadow-lg"
                    >
                      <Navigation className="w-5 h-5" />
                      <span>Xem Chỉ Đường</span>
                      <ExternalLink className="w-4 h-4" />
                    </motion.a>

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => {
                        if (navigator.share) {
                          navigator.share({
                            title: venue.title,
                            text: `${venue.location} - ${venue.address}`,
                            url: venue.mapUrl
                          });
                        } else {
                          navigator.clipboard.writeText(`${venue.location} - ${venue.address} - ${venue.mapUrl}`);
                          alert('Đã sao chép thông tin địa điểm!');
                        }
                      }}
                      className="w-full bg-white/80 text-gray-800 py-3 px-6 rounded-2xl font-didot font-medium text-center border border-gray-200 hover:bg-white hover:shadow-md transition-all duration-300"
                    >
                      Chia Sẻ Địa Điểm
                    </motion.button>
                  </div>

                  {/* Decorative divider */}
                  <motion.div
                    className="flex items-center justify-center mt-6"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                    <span className="mx-3 text-champagne text-lg">🗺️</span>
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Note */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="wedding-card max-w-3xl mx-auto p-8">
            <motion.div
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-3xl mb-4"
            >
              🚗
            </motion.div>

            <h3 className="font-didot text-2xl font-bold text-gray-800 mb-4 tracking-wide">
              Hướng Dẫn Đi Lại
            </h3>
            <p className="text-gray-600 font-didot leading-relaxed mb-4">
              Để thuận tiện cho việc di chuyển, chúng tôi khuyến khích quý khách sử dụng
              các ứng dụng bản đồ như Google Maps hoặc liên hệ số điện thoại trên để được hỗ trợ.
            </p>
            <p className="text-gray-600 font-didot text-sm">
              💡 <strong>Lưu ý:</strong> Nên khởi hành sớm 30 phút để tránh tắc đường
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default LocationMap;
