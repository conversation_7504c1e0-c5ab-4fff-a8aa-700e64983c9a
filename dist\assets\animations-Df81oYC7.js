import{r as t}from"./vendor-DholdlZV.js";var e,n,i={exports:{}},s={};var o=(n||(n=1,i.exports=function(){if(e)return s;e=1;var t=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function i(e,n,i){var s=null;if(void 0!==i&&(s=""+i),void 0!==n.key&&(s=""+n.key),"key"in n)for(var o in i={},n)"key"!==o&&(i[o]=n[o]);else i=n;return n=i.ref,{$$typeof:t,type:e,key:s,ref:void 0!==n?n:null,props:i}}return s.Fragment=n,s.jsx=i,s.jsxs=i,s}()),i.exports),r=t();const a=r.createContext({});const l="undefined"!=typeof window,u=l?r.useLayoutEffect:r.useEffect,h=r.createContext(null);function c(t,e){-1===t.indexOf(e)&&t.push(e)}function d(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const p=(t,e,n)=>n>e?e:n<t?t:n;const m={},f=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function y(t){return"object"==typeof t&&null!==t}const g=t=>/^0[^.\s]+$/u.test(t);function v(t){let e;return()=>(void 0===e&&(e=t()),e)}const x=t=>t,T=(t,e)=>n=>e(t(n)),w=(...t)=>t.reduce(T),P=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class S{constructor(){this.subscriptions=[]}add(t){return c(this.subscriptions,t),()=>d(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const b=t=>1e3*t,A=t=>t/1e3;function E(t,e){return e?t*(1e3/e):0}const V=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function M(t,e,n,i){if(t===e&&n===i)return x;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=V(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:V(s(t),e,i)}const C=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,D=t=>e=>1-t(1-e),k=M(.33,1.53,.69,.99),R=D(k),L=C(R),j=t=>(t*=2)<1?.5*R(t):.5*(2-Math.pow(2,-10*(t-1))),B=t=>1-Math.sin(Math.acos(t)),F=D(B),O=C(B),I=M(.42,0,1,1),U=M(0,0,.58,1),N=M(.42,0,.58,1),W=t=>Array.isArray(t)&&"number"==typeof t[0],$={linear:x,easeIn:I,easeInOut:N,easeOut:U,circIn:B,circInOut:O,circOut:F,backIn:R,backInOut:L,backOut:k,anticipate:j},Y=t=>{if(W(t)){t.length;const[e,n,i,s]=t;return M(e,n,i,s)}return"string"==typeof t?$[t]:t},X=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],K={value:null};function z(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=X.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(h.schedule(e),t()),l++,e(a)}const h={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&K.value&&K.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,h.process(t)))}};return h}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:f}=r,y=()=>{const o=m.useManualTiming?s.timestamp:performance.now();n=!1,m.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),c.process(s),d.process(s),p.process(s),f.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(y))};return{schedule:X.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(y)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<X.length;e++)r[X[e]].cancel(t)},state:s,steps:r}}const{schedule:H,cancel:q,state:G,steps:Z}=z("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:x,!0);let _;function J(){_=void 0}const Q={now:()=>(void 0===_&&Q.set(G.isProcessing||m.useManualTiming?G.timestamp:performance.now()),_),set:t=>{_=t,queueMicrotask(J)}},tt=t=>e=>"string"==typeof e&&e.startsWith(t),et=tt("--"),nt=tt("var(--"),it=t=>!!nt(t)&&st.test(t.split("/*")[0].trim()),st=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ot={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},rt={...ot,transform:t=>p(0,1,t)},at={...ot,default:1},lt=t=>Math.round(1e5*t)/1e5,ut=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ht=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ct=(t,e)=>n=>Boolean("string"==typeof n&&ht.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),dt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(ut);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},pt={...ot,transform:t=>Math.round((t=>p(0,255,t))(t))},mt={test:ct("rgb","red"),parse:dt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+pt.transform(t)+", "+pt.transform(e)+", "+pt.transform(n)+", "+lt(rt.transform(i))+")"};const ft={test:ct("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:mt.transform},yt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),gt=yt("deg"),vt=yt("%"),xt=yt("px"),Tt=yt("vh"),wt=yt("vw"),Pt=(()=>({...vt,parse:t=>vt.parse(t)/100,transform:t=>vt.transform(100*t)}))(),St={test:ct("hsl","hue"),parse:dt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+vt.transform(lt(e))+", "+vt.transform(lt(n))+", "+lt(rt.transform(i))+")"},bt={test:t=>mt.test(t)||ft.test(t)||St.test(t),parse:t=>mt.test(t)?mt.parse(t):St.test(t)?St.parse(t):ft.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?mt.transform(t):St.transform(t),getAnimatableNone:t=>{const e=bt.parse(t);return e.alpha=0,bt.transform(e)}},At=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Et="number",Vt="color",Mt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ct(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Mt,t=>(bt.test(t)?(i.color.push(o),s.push(Vt),n.push(bt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(Et),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function Dt(t){return Ct(t).values}function kt(t){const{split:e,types:n}=Ct(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===Et?lt(t[o]):e===Vt?bt.transform(t[o]):t[o]}return s}}const Rt=t=>"number"==typeof t?0:bt.test(t)?bt.getAnimatableNone(t):t;const Lt={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(ut))?void 0:e.length)||0)+((null==(n=t.match(At))?void 0:n.length)||0)>0},parse:Dt,createTransformer:kt,getAnimatableNone:function(t){const e=Dt(t);return kt(t)(e.map(Rt))}};function jt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Bt(t,e){return n=>n>0?e:t}const Ft=(t,e,n)=>t+(e-t)*n,Ot=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},It=[ft,mt,St];function Ut(t){const e=(n=t,It.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===St&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=jt(a,i,t+1/3),o=jt(a,i,t),r=jt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Nt=(t,e)=>{const n=Ut(t),i=Ut(e);if(!n||!i)return Bt(t,e);const s={...n};return t=>(s.red=Ot(n.red,i.red,t),s.green=Ot(n.green,i.green,t),s.blue=Ot(n.blue,i.blue,t),s.alpha=Ft(n.alpha,i.alpha,t),mt.transform(s))},Wt=new Set(["none","hidden"]);function $t(t,e){return n=>Ft(t,e,n)}function Yt(t){return"number"==typeof t?$t:"string"==typeof t?it(t)?Bt:bt.test(t)?Nt:zt:Array.isArray(t)?Xt:"object"==typeof t?bt.test(t)?Nt:Kt:Bt}function Xt(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>Yt(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Kt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=Yt(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const zt=(t,e)=>{const n=Lt.createTransformer(e),i=Ct(t),s=Ct(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Wt.has(t)&&!s.values.length||Wt.has(e)&&!i.values.length?function(t,e){return Wt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):w(Xt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):Bt(t,e)};function Ht(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Ft(t,e,n);return Yt(t)(t,e)}const qt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>H.update(e,t),stop:()=>q(e),now:()=>G.isProcessing?G.timestamp:Q.now()}},Gt=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=Math.round(1e4*t(o/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},Zt=2e4;function _t(t){let e=0;let n=t.next(e);for(;!n.done&&e<Zt;)e+=50,n=t.next(e);return e>=Zt?1/0:e}function Jt(t,e,n){const i=Math.max(e-5,0);return E(n-t(i),e-i)}const Qt=100,te=10,ee=1,ne=0,ie=800,se=.3,oe=.3,re={granular:.01,default:2},ae={granular:.005,default:.5},le=.01,ue=10,he=.05,ce=1,de=.001;function pe({duration:t=ie,bounce:e=se,velocity:n=ne,mass:i=ee}){let s,o,r=1-e;r=p(he,ce,r),t=p(le,ue,A(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=fe(e,r),l=Math.exp(-s);return de-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=fe(Math.pow(e,2),r);return(-s(e)+de>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<me;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=b(t),isNaN(a))return{stiffness:Qt,damping:te,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const me=12;function fe(t,e){return t*Math.sqrt(1-e*e)}const ye=["duration","bounce"],ge=["stiffness","damping","mass"];function ve(t,e){return e.some(e=>void 0!==t[e])}function xe(t=oe,e=se){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:m}=function(t){let e={velocity:ne,stiffness:Qt,damping:te,mass:ee,isResolvedFromDuration:!1,...t};if(!ve(t,ge)&&ve(t,ye))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*p(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:ee,stiffness:s,damping:o}}else{const n=pe(t);e={...e,...n,mass:ee},e.isResolvedFromDuration=!0}return e}({...n,velocity:-A(n.velocity||0)}),f=d||0,y=u/(2*Math.sqrt(l*h)),g=r-o,v=A(Math.sqrt(l/h)),x=Math.abs(g)<5;let T;if(i||(i=x?re.granular:re.default),s||(s=x?ae.granular:ae.default),y<1){const t=fe(v,y);T=e=>{const n=Math.exp(-y*v*e);return r-n*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)T=t=>r-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);T=e=>{const n=Math.exp(-y*v*e),i=Math.min(t*e,300);return r-n*((f+y*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:m&&c||null,next:t=>{const e=T(t);if(m)a.done=t>=c;else{let n=0===t?f:0;y<1&&(n=0===t?b(f):Jt(T,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(_t(w),Zt),e=Gt(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Te({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=xe({keyframes:[d.value,p(d.value)],velocity:Jt(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function we(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||m.mix||Ht,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||x:e;n=w(t,n)}i.push(n)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=P(t[i],t[i+1],n);return a[i](s)};return n?e=>u(p(t[0],t[o-1],e)):u}function Pe(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=P(0,e,i);t.push(Ft(n,1,s))}}(e,t.length-1),e}function Se({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(Y):Y(i),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Pe(e),t),a=we(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map(()=>u||N).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}xe.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(_t(i),Zt);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:A(s)}}(t,100,xe);return t.ease=e.ease,t.duration=b(e.duration),t.type="keyframes",t};const be=t=>null!==t;function Ae(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(be),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ee={decay:Te,inertia:Te,tween:Se,keyframes:Se,spring:xe};function Ve(t){"string"==typeof t.type&&(t.type=Ee[t.type])}class Me{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ce=t=>t/100;class De extends Me{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var t,e;const{motionValue:n}=this.options;n&&n.updatedAt!==Q.now()&&this.tick(Q.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(e=(t=this.options).onStop)||e.call(t))},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ve(t);const{type:e=Se,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Se;a!==Se&&"number"!=typeof r[0]&&(this.mixKeyframes=w(Ce,Ht(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=_t(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let x=this.currentTime,T=n;if(h){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===c?(n=1-n,d&&(n-=d/r)):"mirror"===c&&(T=o)),x=p(0,1,n)*r}const w=v?{done:!1,value:u[0]}:T.next(x);s&&(w.value=s(w.value));let{done:P}=w;v||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&m!==Te&&(w.value=Ae(u,this.options,y,this.speed)),f&&f(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return A(this.calculatedDuration)}get time(){return A(this.currentTime)}set time(t){var e;t=b(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),null==(e=this.driver)||e.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Q.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=A(this.currentTime))}play(){var t,e;if(this.isStopped)return;const{driver:n=qt,startTime:i}=this.options;this.driver||(this.driver=n(t=>this.tick(t))),null==(e=(t=this.options).onPlay)||e.call(t);const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=i??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Q.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(e=this.driver)||e.stop(),t.observe(this)}}const ke=t=>180*t/Math.PI,Re=t=>{const e=ke(Math.atan2(t[1],t[0]));return je(e)},Le={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Re,rotateZ:Re,skewX:t=>ke(Math.atan(t[1])),skewY:t=>ke(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},je=t=>((t%=360)<0&&(t+=360),t),Be=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Fe=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Oe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Be,scaleY:Fe,scale:t=>(Be(t)+Fe(t))/2,rotateX:t=>je(ke(Math.atan2(t[6],t[5]))),rotateY:t=>je(ke(Math.atan2(-t[2],t[0]))),rotateZ:Re,rotate:Re,skewX:t=>ke(Math.atan(t[4])),skewY:t=>ke(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ie(t){return t.includes("scale")?1:0}function Ue(t,e){if(!t||"none"===t)return Ie(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Oe,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Le,s=e}if(!s)return Ie(e);const o=i[e],r=s[1].split(",").map(Ne);return"function"==typeof o?o(r):r[o]}function Ne(t){return parseFloat(t.trim())}const We=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$e=(()=>new Set(We))(),Ye=t=>t===ot||t===xt,Xe=new Set(["x","y","z"]),Ke=We.filter(t=>!Xe.has(t));const ze={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ue(e,"x"),y:(t,{transform:e})=>Ue(e,"y")};ze.translateX=ze.x,ze.translateY=ze.y;const He=new Set;let qe=!1,Ge=!1,Ze=!1;function _e(){if(Ge){const t=Array.from(He).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Ke.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{var i;null==(i=t.getValue(e))||i.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Ge=!1,qe=!1,He.forEach(t=>t.complete(Ze)),He.clear()}function Je(){He.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ge=!0)})}class Qe{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(He.add(this),qe||(qe=!0,H.read(Je),H.resolveKeyframes(_e))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),He.delete(this)}cancel(){"scheduled"===this.state&&(He.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const tn=v(()=>void 0!==window.ScrollTimeline),en={};function nn(t,e){const n=v(t);return()=>en[e]??n()}const sn=nn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),on=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,rn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:on([0,.65,.55,1]),circOut:on([.55,0,1,.45]),backIn:on([.31,.01,.66,-.59]),backOut:on([.33,1.53,.69,.99])};function an(t,e){return t?"function"==typeof t?sn()?Gt(t,e):"ease-out":W(t)?on(t):Array.isArray(t)?t.map(t=>an(t,e)||rn.easeOut):rn[t]:void 0}function ln(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=an(a,s);Array.isArray(c)&&(h.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);return t.animate(h,d)}function un(t){return"function"==typeof t&&"applyToOptions"in t}class hn extends Me{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return un(t)&&sn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=ln(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Ae(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}null==a||a(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,e;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,e;this.isPseudoElement||null==(e=(t=this.animation).commitStyles)||e.call(t)}get duration(){var t,e;const n=(null==(e=null==(t=this.animation.effect)?void 0:t.getComputedTiming)?void 0:e.call(t).duration)||0;return A(Number(n))}get time(){return A(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=b(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){var n;return this.allowFlatten&&(null==(n=this.animation.effect)||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&tn()?(this.animation.timeline=t,x):e(this)}}const cn={anticipate:j,backInOut:L,circInOut:O};function dn(t){"string"==typeof t.ease&&t.ease in cn&&(t.ease=cn[t.ease])}class pn extends hn{constructor(t){dn(t),Ve(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new De({...o,autoplay:!1}),a=b(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const mn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Lt.test(t)&&"0"!==t||t.startsWith("url(")));function fn(t){return y(t)&&"offsetHeight"in t}const yn=new Set(["opacity","clipPath","filter","transform"]),gn=v(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class vn extends Me{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...h}){var c;super(),this.stop=()=>{var t,e;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=Q.now();const d={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...h},p=(null==u?void 0:u.KeyframeResolver)||Qe;this.keyframeResolver=new p(r,(t,e,n)=>this.onKeyframesResolved(t,e,d,!n),a,l,u),null==(c=this.keyframeResolver)||c.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=Q.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=mn(s,e),a=mn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||un(n))&&i)}(t,s,o,r)||(!m.instantAnimations&&a||null==u||u(Ae(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!l&&function(t){var e;const{motionValue:n,name:i,repeatDelay:s,repeatType:o,damping:r,type:a}=t;if(!fn(null==(e=null==n?void 0:n.owner)?void 0:e.current))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return gn()&&i&&yn.has(i)&&("transform"!==i||!u)&&!l&&!s&&"mirror"!==o&&0!==r&&"inertia"!==a}(h)?new pn({...h,element:h.motionValue.owner.current}):new De(h);c.finished.then(()=>this.notifyFinished()).catch(x),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||(null==(t=this.keyframeResolver)||t.resume(),Ze=!0,Je(),_e(),Ze=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),null==(t=this.keyframeResolver)||t.cancel()}}const xn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Tn(t,e,n=1){const[i,s]=function(t){const e=xn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return f(t)?parseFloat(t):t}return it(s)?Tn(s,e,n+1):s}function wn(t,e){return(null==t?void 0:t[e])??(null==t?void 0:t.default)??t}const Pn=new Set(["width","height","top","left","right","bottom",...We]),Sn=t=>e=>e.test(t),bn=[ot,xt,vt,gt,wt,Tt,{test:t=>"auto"===t,parse:t=>t}],An=t=>bn.find(Sn(t));function En(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||g(t))}const Vn=new Set(["brightness","contrast","saturate","opacity"]);function Mn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(ut)||[];if(!i)return t;const s=n.replace(i,"");let o=Vn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Cn=/\b([a-z-]*)\(.*?\)/gu,Dn={...Lt,getAnimatableNone:t=>{const e=t.match(Cn);return e?e.map(Mn).join(" "):t}},kn={...ot,transform:Math.round},Rn={borderWidth:xt,borderTopWidth:xt,borderRightWidth:xt,borderBottomWidth:xt,borderLeftWidth:xt,borderRadius:xt,radius:xt,borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomRightRadius:xt,borderBottomLeftRadius:xt,width:xt,maxWidth:xt,height:xt,maxHeight:xt,top:xt,right:xt,bottom:xt,left:xt,padding:xt,paddingTop:xt,paddingRight:xt,paddingBottom:xt,paddingLeft:xt,margin:xt,marginTop:xt,marginRight:xt,marginBottom:xt,marginLeft:xt,backgroundPositionX:xt,backgroundPositionY:xt,...{rotate:gt,rotateX:gt,rotateY:gt,rotateZ:gt,scale:at,scaleX:at,scaleY:at,scaleZ:at,skew:gt,skewX:gt,skewY:gt,distance:xt,translateX:xt,translateY:xt,translateZ:xt,x:xt,y:xt,z:xt,perspective:xt,transformPerspective:xt,opacity:rt,originX:Pt,originY:Pt,originZ:xt},zIndex:kn,fillOpacity:rt,strokeOpacity:rt,numOctaves:kn},Ln={...Rn,color:bt,backgroundColor:bt,outlineColor:bt,fill:bt,stroke:bt,borderColor:bt,borderTopColor:bt,borderRightColor:bt,borderBottomColor:bt,borderLeftColor:bt,filter:Dn,WebkitFilter:Dn},jn=t=>Ln[t];function Bn(t,e){let n=jn(t);return n!==Dn&&(n=Lt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Fn=new Set(["auto","none","0"]);class On extends Qe{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),it(n))){const i=Tn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!Pn.has(n)||2!==t.length)return;const[i,s]=t,o=An(i),r=An(s);if(o!==r)if(Ye(o)&&Ye(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else ze[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let i=0;i<t.length;i++)(null===t[i]||En(t[i]))&&n.push(i);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Fn.has(e)&&Ct(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Bn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ze[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=ze[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}function In(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}const Un=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Nn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{var n,i;const s=Q.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(null==(n=this.events.change)||n.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty();e&&(null==(i=this.events.renderRequest)||i.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new S);const n=this.events[t].add(e);return"change"===t?()=>{n(),H.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;null==(t=this.events.change)||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return E(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,e;null==(t=this.dependents)||t.clear(),null==(e=this.events.destroy)||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Wn(t,e){return new Nn(t,e)}const{schedule:$n}=z(queueMicrotask,!1),Yn={x:!1,y:!1};function Xn(){return Yn.x||Yn.y}function Kn(t,e){const n=In(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function zn(t){return!("touch"===t.pointerType||Xn())}const Hn=(t,e)=>!!e&&(t===e||Hn(t,e.parentElement)),qn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Gn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Zn=new WeakSet;function _n(t){return e=>{"Enter"===e.key&&t(e)}}function Jn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Qn(t){return qn(t)&&!Xn()}function ti(t,e,n={}){const[i,s,o]=Kn(t,n),r=t=>{const i=t.currentTarget;if(!Qn(t))return;Zn.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Zn.has(i)&&Zn.delete(i),Qn(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Hn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),fn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=_n(()=>{if(Zn.has(n))return;Jn(n,"down");const t=_n(()=>{Jn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Jn(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),e=t,Gn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function ei(t){return y(t)&&"ownerSVGElement"in t}const ni=t=>Boolean(t&&t.getVelocity),ii=[...bn,bt,Lt],si=r.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});const oi=r.createContext({strict:!1}),ri={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ai={};for(const aa in ri)ai[aa]={isEnabled:t=>ri[aa].some(e=>!!t[e])};const li=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ui(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||li.has(t)}let hi=t=>!ui(t);try{"function"==typeof(ci=require("@emotion/is-prop-valid").default)&&(hi=t=>t.startsWith("on")?!ui(t):ci(t))}catch{}var ci;function di(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>t(...e),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const pi=r.createContext({});function mi(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function fi(t){return"string"==typeof t||Array.isArray(t)}const yi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],gi=["initial",...yi];function vi(t){return mi(t.animate)||gi.some(e=>fi(t[e]))}function xi(t){return Boolean(vi(t)||t.variants)}function Ti(t){const{initial:e,animate:n}=function(t,e){if(vi(t)){const{initial:e,animate:n}=t;return{initial:!1===e||fi(e)?e:void 0,animate:fi(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,r.useContext(pi));return r.useMemo(()=>({initial:e,animate:n}),[wi(e),wi(n)])}function wi(t){return Array.isArray(t)?t.join(" "):t}const Pi=Symbol.for("motionComponentSymbol");function Si(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function bi(t,e,n){return r.useCallback(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&("function"==typeof n?n(i):Si(n)&&(n.current=i))},[e])}const Ai=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Ei="data-"+Ai("framerAppearId"),Vi=r.createContext({});function Mi(t,e,n,i,s){var o,a;const{visualElement:l}=r.useContext(pi),c=r.useContext(oi),d=r.useContext(h),p=r.useContext(si).reducedMotion,m=r.useRef(null);i=i||c.renderer,!m.current&&i&&(m.current=i(t,{visualState:e,parent:l,props:n,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:p}));const f=m.current,y=r.useContext(Vi);!f||f.projection||!s||"html"!==f.type&&"svg"!==f.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ci(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&Si(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(m.current,n,s,y);const g=r.useRef(!1);r.useInsertionEffect(()=>{f&&g.current&&f.update(n,d)});const v=n[Ei],x=r.useRef(Boolean(v)&&!(null==(o=window.MotionHandoffIsComplete)?void 0:o.call(window,v))&&(null==(a=window.MotionHasOptimisedAnimation)?void 0:a.call(window,v)));return u(()=>{f&&(g.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),$n.render(f.render),x.current&&f.animationState&&f.animationState.animateChanges())}),r.useEffect(()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,v)}),x.current=!1))}),f}function Ci(t){if(t)return!1!==t.options.allowProjection?t.projection:Ci(t.parent)}function Di({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:i,Component:s}){function a(t,a){let u;const h={...r.useContext(si),...t,layoutId:ki(t)},{isStatic:c}=h,d=Ti(t),p=i(t,c);if(!c&&l){r.useContext(oi).strict;const t=function(t){const{drag:e,layout:n}=ai;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=Mi(s,p,h,e,t.ProjectionNode)}return o.jsxs(pi.Provider,{value:d,children:[u&&d.visualElement?o.jsx(u,{visualElement:d.visualElement,...h}):null,n(s,t,bi(p,d.visualElement,a),p,c,d.visualElement)]})}t&&function(t){for(const e in t)ai[e]={...ai[e],...t[e]}}(t),a.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;const u=r.forwardRef(a);return u[Pi]=s,u}function ki({layoutId:t}){const e=r.useContext(a).id;return e&&void 0!==t?e+"-"+t:t}const Ri={};function Li(t,{layout:e,layoutId:n}){return $e.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ri[t]||"opacity"===t)}const ji={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Bi=We.length;function Fi(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if($e.has(l))r=!0;else if(et(l))s[l]=t;else{const e=Un(t,Rn[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Bi;o++){const r=We[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=Un(a,Rn[r]);l||(s=!1,i+=`${ji[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Oi=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ii(t,e,n){for(const i in e)ni(e[i])||Li(i,n)||(t[i]=e[i])}function Ui(t,e){const n={};return Ii(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},e){return r.useMemo(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{}};return Fi(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),n}function Ni(t,e){const n={},i=Ui(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Wi={offset:"stroke-dashoffset",array:"stroke-dasharray"},$i={offset:"strokeDashoffset",array:"strokeDasharray"};function Yi(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,h){if(Fi(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=(null==h?void 0:h.transformBox)??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Wi:$i;t[o.offset]=xt.transform(-i);const r=xt.transform(e),a=xt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const Xi=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Ki=t=>"string"==typeof t&&"svg"===t.toLowerCase();function zi(t,e,n,i){const s=r.useMemo(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Yi(n,e,Ki(i),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){const e={};Ii(e,t.style,t),s.style={...e,...s.style}}return s}const Hi=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function qi(t){return"string"==typeof t&&!t.includes("-")&&!!(Hi.indexOf(t)>-1||/[A-Z]/u.test(t))}function Gi(t=!1){return(e,n,i,{latestValues:s},o)=>{const a=(qi(e)?zi:Ni)(n,s,o,e),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(hi(s)||!0===n&&ui(s)||!e&&!ui(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(n,"string"==typeof e,t),u=e!==r.Fragment?{...l,...a,ref:i}:{},{children:h}=n,c=r.useMemo(()=>ni(h)?h.get():h,[h]);return r.createElement(e,{...u,children:c})}}function Zi(t){const e=[{},{}];return null==t||t.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function _i(t,e,n,i){if("function"==typeof e){const[s,o]=Zi(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=Zi(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function Ji(t){return ni(t)?t.get():t}const Qi=t=>(e,n)=>{const i=r.useContext(pi),s=r.useContext(h),o=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:ts(n,i,s,t),renderState:e()}}(t,e,i,s);return n?o():function(t){const e=r.useRef(null);return null===e.current&&(e.current=t()),e.current}(o)};function ts(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=Ji(o[d]);let{initial:r,animate:a}=t;const l=vi(t),u=xi(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let h=!!n&&!1===n.initial;h=h||!1===r;const c=h?a:r;if(c&&"boolean"!=typeof c&&!mi(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=_i(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[h?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}function es(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(ni(s[r])||e.style&&ni(e.style[r])||Li(r,t)||void 0!==(null==(i=null==n?void 0:n.getValue(r))?void 0:i.liveStyle))&&(o[r]=s[r]);return o}const ns={useVisualState:Qi({scrapeMotionValuesFromProps:es,createRenderState:Oi})};function is(t,e,n){const i=es(t,e,n);for(const s in t)if(ni(t[s])||ni(e[s])){i[-1!==We.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const ss={useVisualState:Qi({scrapeMotionValuesFromProps:is,createRenderState:Xi})};function os(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Di({...qi(n)?ss:ns,preloadedFeatures:t,useRender:Gi(i),createVisualElement:e,Component:n})}}function rs(t,e,n){const i=t.getProps();return _i(i,e,void 0!==n?n:i.custom,t)}const as=t=>Array.isArray(t);function ls(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Wn(n))}function us(t){return as(t)?t[t.length-1]||0:t}function hs(t,e){const n=t.getValue("willChange");if(i=n,Boolean(ni(i)&&i.add))return n.add(e);if(!n&&m.WillChange){const n=new m.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function cs(t){return t.props[Ei]}const ds=t=>null!==t;const ps={type:"spring",stiffness:500,damping:25,restSpeed:10},ms={type:"keyframes",duration:.8},fs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ys=(t,{keyframes:e})=>e.length>2?ms:$e.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ps:fs;const gs=(t,e,n,i={},s,o)=>r=>{const a=wn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=b(l);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(a)||Object.assign(h,ys(t,h)),h.duration&&(h.duration=b(h.duration)),h.repeatDelay&&(h.repeatDelay=b(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(c=!0)),(m.instantAnimations||m.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"}){const i=t.filter(ds);return i[e&&"loop"!==n&&e%2==1?0:i.length-1]}(h.keyframes,a);if(void 0!==t)return void H.update(()=>{h.onUpdate(t),h.onComplete()})}return a.isSync?new De(h):new vn(h)};function vs({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function xs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const h in a){const e=t.getValue(h,t.latestValues[h]??null),i=a[h];if(void 0===i||u&&vs(u,h))continue;const s={delay:n,...wn(o||{},h)},r=e.get();if(void 0!==r&&!e.isAnimating&&!Array.isArray(i)&&i===r&&!s.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const e=cs(t);if(e){const t=window.MotionHandoffAnimation(e,h,H);null!==t&&(s.startTime=t,c=!0)}}hs(t,h),e.start(gs(h,e,i,t.shouldReduceMotion&&Pn.has(h)?{type:!1}:s,t,c));const d=e.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{H.update(()=>{r&&function(t,e){const n=rs(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const r in o)ls(t,r,us(o[r]))}(t,r)})}),l}function Ts(t,e,n={}){var i;const s=rs(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(xs(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(ws).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(Ts(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}return Promise.all([r(),a(n.delay)])}function ws(t,e){return t.sortNodePosition(e)}function Ps(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const Ss=gi.length;function bs(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&bs(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Ss;n++){const i=gi[n],s=t.props[i];(fi(s)||!1===s)&&(e[i]=s)}return e}const As=[...yi].reverse(),Es=yi.length;function Vs(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>Ts(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=Ts(t,e,n);else{const s="function"==typeof e?rs(t,e,n.custom):e;i=Promise.all(xs(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function Ms(t){let e=Vs(t),n=ks(),i=!0;const s=e=>(n,i)=>{var s;const o=rs(t,i,"exit"===e?null==(s=t.presenceContext)?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=bs(t.parent)||{},l=[],u=new Set;let h={},c=1/0;for(let e=0;e<Es;e++){const d=As[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=fi(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&i&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...h},!p.isActive&&null===y||!m&&!p.prevProp||mi(m)||"boolean"==typeof m)continue;const v=Cs(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const w=Array.isArray(m)?m:[m];let P=w.reduce(s(d),{});!1===y&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,u.has(e)&&(T=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(h.hasOwnProperty(t))continue;let i=!1;i=as(e)&&as(n)?!Ps(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(h={...h,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(g&&v)||T)&&l.push(...w.map(t=>({animation:t,options:{type:d}})))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=rs(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null==(s=t.variantChildren)||s.forEach(t=>{var n;return null==(n=t.animationState)?void 0:n.setActive(e,i)}),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=ks(),i=!0}}}function Cs(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Ps(e,t)}function Ds(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ks(){return{animate:Ds(!0),whileInView:Ds(),whileHover:Ds(),whileTap:Ds(),whileDrag:Ds(),whileFocus:Ds(),exit:Ds()}}class Rs{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Ls=0;const js={animation:{Feature:class extends Rs{constructor(t){super(t),t.animationState||(t.animationState=Ms(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();mi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}},exit:{Feature:class extends Rs{constructor(){super(...arguments),this.id=Ls++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Bs(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Fs(t){return{point:{x:t.pageX,y:t.pageY}}}function Os(t,e,n,i){return Bs(t,e,(t=>e=>qn(e)&&t(e,Fs(e)))(n),i)}function Is({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Us(t){return t.max-t.min}function Ns(t,e,n,i=.5){t.origin=i,t.originPoint=Ft(e.min,e.max,t.origin),t.scale=Us(n)/Us(e),t.translate=Ft(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Ws(t,e,n,i){Ns(t.x,e.x,n.x,i?i.originX:void 0),Ns(t.y,e.y,n.y,i?i.originY:void 0)}function $s(t,e,n){t.min=n.min+e.min,t.max=t.min+Us(e)}function Ys(t,e,n){t.min=e.min-n.min,t.max=t.min+Us(e)}function Xs(t,e,n){Ys(t.x,e.x,n.x),Ys(t.y,e.y,n.y)}const Ks=()=>({x:{min:0,max:0},y:{min:0,max:0}});function zs(t){return[t("x"),t("y")]}function Hs(t){return void 0===t||1===t}function qs({scale:t,scaleX:e,scaleY:n}){return!Hs(t)||!Hs(e)||!Hs(n)}function Gs(t){return qs(t)||Zs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Zs(t){return _s(t.x)||_s(t.y)}function _s(t){return t&&"0%"!==t}function Js(t,e,n){return n+e*(t-n)}function Qs(t,e,n,i,s){return void 0!==s&&(t=Js(t,s,i)),Js(t,n,i)+e}function to(t,e=0,n=1,i,s){t.min=Qs(t.min,e,n,i,s),t.max=Qs(t.max,e,n,i,s)}function eo(t,{x:e,y:n}){to(t.x,e.translate,e.scale,e.originPoint),to(t.y,n.translate,n.scale,n.originPoint)}const no=.999999999999,io=1.0000000000001;function so(t,e){t.min=t.min+e,t.max=t.max+e}function oo(t,e,n,i,s=.5){to(t,e,n,Ft(t.min,t.max,s),i)}function ro(t,e){oo(t.x,e.x,e.scaleX,e.scale,e.originX),oo(t.y,e.y,e.scaleY,e.scale,e.originY)}function ao(t,e){return Is(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const lo=({current:t})=>t?t.ownerDocument.defaultView:null,uo=(t,e)=>Math.abs(t-e);class ho{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=mo(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=uo(t.x,e.x),i=uo(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=G;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=co(e,this.transformPagePoint),H.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=mo("pointercancel"===t.type?this.lastMoveEventInfo:co(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!qn(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=co(Fs(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=G;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,mo(o,this.history)),this.removeListeners=w(Os(this.contextWindow,"pointermove",this.handlePointerMove),Os(this.contextWindow,"pointerup",this.handlePointerUp),Os(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function co(t,e){return e?{point:e(t.point)}:t}function po(t,e){return{x:t.x-e.x,y:t.y-e.y}}function mo({point:t},e){return{point:t,delta:po(t,yo(e)),offset:po(t,fo(e)),velocity:go(e,.1)}}function fo(t){return t[0]}function yo(t){return t[t.length-1]}function go(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=yo(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>b(e)));)n--;if(!i)return{x:0,y:0};const o=A(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function vo(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function xo(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const To=.35;function wo(t,e,n){return{min:Po(t,e),max:Po(t,n)}}function Po(t,e){return"number"==typeof t?t:t[e]||0}const So=new WeakMap;class bo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new ho(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Fs(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Yn[o]?null:(Yn[o]=!0,()=>{Yn[o]=!1}):Yn.x||Yn.y?null:(Yn.x=Yn.y=!0,()=>{Yn.x=Yn.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),zs(t=>{let e=this.getAxisMotionValue(t).get()||0;if(vt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Us(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&H.postRender(()=>s(t,e)),hs(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>zs(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:lo(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&H.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Ao(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Ft(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Ft(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,s=this.constraints;e&&Si(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:vo(t.x,n,s),y:vo(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=To){return!1===t?t=0:!0===t&&(t=To),{x:wo(t,"left","right"),y:wo(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&zs(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Si(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=ao(t,n),{scroll:s}=e;return s&&(so(i.x,s.offset.x),so(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:xo(t.x,e.x),y:xo(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Is(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=zs(r=>{if(!Ao(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,h=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return hs(this.visualElement,t),n.start(gs(t,n,0,e,this.visualElement,!1))}stopAnimation(){zs(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){zs(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){zs(e=>{const{drag:n}=this.getProps();if(!Ao(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Ft(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!Si(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};zs(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Us(t),s=Us(e);return s>i?n=P(e.min,e.max-i,t.min):i>s&&(n=P(t.min,t.max-s,e.min)),p(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),zs(e=>{if(!Ao(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Ft(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;So.set(this.visualElement,this);const t=Os(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();Si(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),H.read(e);const s=Bs(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(zs(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=To,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Ao(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Eo=t=>(e,n)=>{t&&H.postRender(()=>t(e,n))};const Vo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Mo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Co={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!xt.test(t))return t;t=parseFloat(t)}return`${Mo(t,e.target.x)}% ${Mo(t,e.target.y)}%`}},Do={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Lt.parse(t);if(s.length>5)return i;const o=Lt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Ft(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class ko extends r.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)Ri[e]=t[e],et(e)&&(Ri[e].isCSSVariable=!0)}(Lo),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Vo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||H.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),$n.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Ro(t){const[e,n]=function(t=!0){const e=r.useContext(h);if(null===e)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=e,o=r.useId();r.useEffect(()=>{if(t)return s(o)},[t]);const a=r.useCallback(()=>t&&i&&i(o),[o,i,t]);return!n&&i?[!1,a]:[!0]}(),i=r.useContext(a);return o.jsx(ko,{...t,layoutGroup:i,switchLayoutGroup:r.useContext(Vi),isPresent:e,safeToRemove:n})}const Lo={borderRadius:{...Co,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Co,borderTopRightRadius:Co,borderBottomLeftRadius:Co,borderBottomRightRadius:Co,boxShadow:Do};const jo=(t,e)=>t.depth-e.depth;class Bo{constructor(){this.children=[],this.isDirty=!1}add(t){c(this.children,t),this.isDirty=!0}remove(t){d(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(jo),this.isDirty=!1,this.children.forEach(t)}}const Fo=["TopLeft","TopRight","BottomLeft","BottomRight"],Oo=Fo.length,Io=t=>"string"==typeof t?parseFloat(t):t,Uo=t=>"number"==typeof t||xt.test(t);function No(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Wo=Yo(0,.5,F),$o=Yo(.5,.95,x);function Yo(t,e,n){return i=>i<t?0:i>e?1:n(P(t,e,i))}function Xo(t,e){t.min=e.min,t.max=e.max}function Ko(t,e){Xo(t.x,e.x),Xo(t.y,e.y)}function zo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Ho(t,e,n,i,s){return t=Js(t-=e,1/n,i),void 0!==s&&(t=Js(t,1/s,i)),t}function qo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){vt.test(e)&&(e=parseFloat(e),e=Ft(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Ft(o.min,o.max,i);t===o&&(a-=e),t.min=Ho(t.min,e,n,a,s),t.max=Ho(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Go=["x","scaleX","originX"],Zo=["y","scaleY","originY"];function _o(t,e,n,i){qo(t.x,e,Go,n?n.x:void 0,i?i.x:void 0),qo(t.y,e,Zo,n?n.y:void 0,i?i.y:void 0)}function Jo(t){return 0===t.translate&&1===t.scale}function Qo(t){return Jo(t.x)&&Jo(t.y)}function tr(t,e){return t.min===e.min&&t.max===e.max}function er(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nr(t,e){return er(t.x,e.x)&&er(t.y,e.y)}function ir(t){return Us(t.x)/Us(t.y)}function sr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class or{constructor(){this.members=[]}add(t){c(this.members,t),t.scheduleRender()}remove(t){if(d(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const rr=["","X","Y","Z"],ar={visibility:"hidden"};let lr=0;function ur(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function hr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=cs(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",H,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&hr(i)}function cr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=lr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(mr),this.nodes.forEach(wr),this.nodes.forEach(Pr),this.nodes.forEach(fr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Bo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new S),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=ei(e)&&!(ei(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=Q.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(q(i),t(o-e))};return H.setup(i,!0),()=>q(i)}(i,250),Vo.hasAnimatedSinceResize&&(Vo.hasAnimatedSinceResize=!1,this.nodes.forEach(Tr))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Mr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!nr(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...wn(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||Tr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Sr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&hr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(gr);this.isUpdating||this.nodes.forEach(vr),this.isUpdating=!1,this.nodes.forEach(xr),this.nodes.forEach(dr),this.nodes.forEach(pr),this.clearAllSnapshots();const t=Q.now();G.delta=p(0,1e3/60,t-G.timestamp),G.timestamp=t,G.isProcessing=!0,Z.update.process(G),Z.preRender.process(G),Z.render.process(G),G.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,$n.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yr),this.sharedNodes.forEach(br)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,H.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){H.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Us(this.snapshot.measuredBox.x)||Us(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Qo(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||Gs(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),kr((i=n).x),kr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(Lr))){const{scroll:t}=this.root;t&&(so(n.x,t.offset.x),so(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(Ko(n,t),null==(e=this.scroll)?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Ko(n,t),so(n.x,s.offset.x),so(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Ko(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&ro(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),Gs(t.latestValues)&&ro(n,t.latestValues)}return Gs(this.latestValues)&&ro(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Ko(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!Gs(t.latestValues))continue;qs(t.latestValues)&&t.updateSnapshot();const i=Ks();Ko(i,t.measurePageBox()),_o(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return Gs(this.latestValues)&&_o(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=G.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Xs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Ko(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,a,l;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,$s(r.x,a.x,l.x),$s(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ko(this.target,this.layout.layoutBox),eo(this.target,this.targetDelta)):Ko(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Xs(this.relativeTargetOrigin,this.target,t.target),Ko(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!qs(this.parent.latestValues)&&!Zs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===G.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Ko(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ro(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eo(t,r)),i&&Gs(o.latestValues)&&ro(t,o.latestValues))}e.x<io&&e.x>no&&(e.x=1),e.y<io&&e.y>no&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(zo(this.prevProjectionDelta.x,this.projectionDelta.x),zo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Ws(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sr(this.projectionDelta.x,this.prevProjectionDelta.x)&&sr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,h=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Vr));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;Ar(o.x,t.x,n),Ar(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Xs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,Er(p.x,m.x,f.x,y),Er(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,tr(l.x,d.x)&&tr(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),Ko(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Ft(0,n.opacity??1,Wo(i)),t.opacityExit=Ft(e.opacity??1,0,$o(i))):o&&(t.opacity=Ft(e.opacity??1,n.opacity??1,i));for(let r=0;r<Oo;r++){const s=`border${Fo[r]}Radius`;let o=No(e,s),a=No(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Uo(o)===Uo(a)?(t[s]=Math.max(Ft(Io(o),Io(a),i),0),(vt.test(a)||vt.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=Ft(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){var e,n,i;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(i=null==(n=this.resumingFrom)?void 0:n.currentAnimation)||i.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=H.update(()=>{Vo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Wn(0)),this.currentAnimation=function(t,e,n){const i=ni(t)?t:Wn(t);return i.start(gs("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Rr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Us(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Us(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Ko(e,n),ro(e,s),Ws(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new or);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&ur("z",t,i,this.animationValues);for(let s=0;s<rr.length;s++)ur(`rotate${rr[s]}`,t,i,this.animationValues),ur(`skew${rr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ar;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=Ji(null==t?void 0:t.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Ji(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!Gs(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const a in Ri){if(void 0===s[a])continue;const{correct:t,applyTo:n,isCSSVariable:o}=Ri[a],r="none"===e.transform?s[a]:t(s[a],i);if(n){const t=n.length;for(let i=0;i<t;i++)e[n[i]]=r}else o?this.options.visualElement.renderState.vars[a]=r:e[a]=r}return this.options.layoutId&&(e.pointerEvents=i===this?Ji(null==t?void 0:t.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(gr),this.root.sharedNodes.clear()}}}function dr(t){t.updateLayout()}function pr(t){var e;const n=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?zs(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Us(i);i.min=e[t].min,i.max=i.min+s}):Rr(s,n.layoutBox,e)&&zs(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Us(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ws(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Ws(a,t.applyTransform(i,!0),n.measuredBox):Ws(a,e,n.layoutBox);const l=!Qo(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Xs(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Xs(a,e,o.layoutBox),nr(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function mr(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function fr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function yr(t){t.clearSnapshot()}function gr(t){t.clearMeasurements()}function vr(t){t.isLayoutDirty=!1}function xr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Tr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function wr(t){t.resolveTargetDelta()}function Pr(t){t.calcProjection()}function Sr(t){t.resetSkewAndRotation()}function br(t){t.removeLeadSnapshot()}function Ar(t,e,n){t.translate=Ft(e.translate,0,n),t.scale=Ft(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Er(t,e,n,i){t.min=Ft(e.min,n.min,i),t.max=Ft(e.max,n.max,i)}function Vr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Mr={duration:.45,ease:[.4,0,.1,1]},Cr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Dr=Cr("applewebkit/")&&!Cr("chrome/")?Math.round:x;function kr(t){t.min=Dr(t.min),t.max=Dr(t.max)}function Rr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=ir(e),s=ir(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Lr(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}const jr=cr({attachResizeListener:(t,e)=>Bs(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Br={current:void 0},Fr=cr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Br.current){const t=new jr({});t.mount(window),t.setOptions({layoutScroll:!0}),Br.current=t}return Br.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Or={pan:{Feature:class extends Rs{constructor(){super(...arguments),this.removePointerDownListener=x}onPointerDown(t){this.session=new ho(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:lo(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Eo(t),onStart:Eo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&H.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Os(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Rs{constructor(t){super(t),this.removeGroupControls=x,this.removeListeners=x,this.controls=new bo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||x}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Fr,MeasureLayout:Ro}};function Ir(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&H.postRender(()=>s(e,Fs(e)))}function Ur(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&H.postRender(()=>s(e,Fs(e)))}const Nr=new WeakMap,Wr=new WeakMap,$r=t=>{const e=Nr.get(t.target);e&&e(t)},Yr=t=>{t.forEach($r)};function Xr(t,e,n){const i=function({root:t,...e}){const n=t||document;Wr.has(n)||Wr.set(n,{});const i=Wr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Yr,{root:t,...e})),i[s]}(e);return Nr.set(t,n),i.observe(t),()=>{Nr.delete(t),i.unobserve(t)}}const Kr={some:0,all:1};const zr={inView:{Feature:class extends Rs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Kr[i]};return Xr(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Rs{mount(){const{current:t}=this.node;t&&(this.unmount=ti(t,(t,e)=>(Ur(this.node,e,"Start"),(t,{success:e})=>Ur(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Rs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=w(Bs(this.node.current,"focus",()=>this.onFocus()),Bs(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Rs{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Kn(t,n),r=t=>{if(!zn(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{zn(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,(t,e)=>(Ir(this.node,e,"Start"),t=>Ir(this.node,t,"End"))))}unmount(){}}}},Hr={layout:{ProjectionNode:Fr,MeasureLayout:Ro}},qr={current:null},Gr={current:!1};const Zr=new WeakMap;const _r=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Jr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Qe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,H.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=vi(e),this.isVariantNode=xi(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const c in h){const t=h[c];void 0!==a[c]&&ni(t)&&t.set(a[c],!1)}}mount(t){this.current=t,Zr.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Gr.current||function(){if(Gr.current=!0,l)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>qr.current=t.matches;t.addListener(e),e()}else qr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||qr.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=$e.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&H.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ai){const e=ai[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<_r.length;n++){const e=_r[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(ni(s))t.addValue(i,s);else if(ni(o))t.addValue(i,Wn(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Wn(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Wn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(f(n)||g(n))?n=parseFloat(n):(i=n,!ii.find(Sn(i))&&Lt.test(e)&&(n=Bn(t,e))),this.setBaseTarget(t,ni(n)?n.get():n)),ni(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=_i(this.props,n,null==(e=this.presenceContext)?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||ni(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new S),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Qr extends Jr{constructor(){super(...arguments),this.KeyframeResolver=On}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ni(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function ta(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}class ea extends Qr{constructor(){super(...arguments),this.type="html",this.renderInstance=ta}readValueFromInstance(t,e){var n,i;if($e.has(e))return(null==(n=this.projection)?void 0:n.isProjecting)?Ie(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ue(n,e)})(t,e);{const n=(i=t,window.getComputedStyle(i)),s=(et(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ao(t,e)}build(t,e,n){Fi(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return es(t,e,n)}}const na=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ia extends Qr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ks}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if($e.has(e)){const t=jn(e);return t&&t.default||0}return e=na.has(e)?e:Ai(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return is(t,e,n)}build(t,e,n){Yi(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){ta(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(na.has(s)?s:Ai(s),e.attrs[s])}(t,e,0,i)}mount(t){this.isSVGTag=Ki(t.tagName),super.mount(t)}}const sa=di(os({...js,...zr,...Or,...Hr},(t,e)=>qi(t)?new ia(e):new ea(e,{allowProjection:t!==r.Fragment}))),oa={some:0,all:1};function ra(t,{root:e,margin:n,amount:i,once:s=!1,initial:o=!1}={}){const[a,l]=r.useState(o);return r.useEffect(()=>{if(!t.current||s&&a)return;const o={root:e&&e.current||void 0,margin:n,amount:i};return function(t,e,{root:n,margin:i,amount:s="some"}={}){const o=In(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:oa[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}(t.current,()=>(l(!0),s?void 0:()=>l(!1)),o)},[e,t,n,s,i]),a}export{o as j,sa as m,r,ra as u};
//# sourceMappingURL=animations-Df81oYC7.js.map
