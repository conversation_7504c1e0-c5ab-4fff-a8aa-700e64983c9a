import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Sparkles } from 'lucide-react';
import { useState } from 'react';

const Hero = () => {
  const [isLetterOpen, setIsLetterOpen] = useState(false);

  const floatingHearts = Array.from({ length: 9 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute text-rose-gold/30"
      style={{
        left: `${10 + i * 10}%`,
        top: `${Math.random() * 80 + 10}%`,
      }}
      animate={{
        y: [0, -20, 0],
        rotate: [0, 10, -10, 0],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: 4 + Math.random() * 2,
        repeat: Infinity,
        delay: i * 0.5,
      }}
    >
      <Heart className="w-4 h-4" />
    </motion.div>
  ));

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-gradient-to-br from-blush/90 via-champagne/90 to-rose-gold/90" />
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-rose-gold/5 animate-pulse" />
        </div>
      </div>
      
      {/* Floating Hearts Background */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingHearts}
      </div>

      {/* Sparkles Animation */}
      <motion.div
        className="absolute top-20 right-20 text-champagne"
        animate={{
          rotate: 360,
          scale: [1, 1.5, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>

      <motion.div
        className="absolute bottom-20 left-20 text-sage"
        animate={{
          rotate: -360,
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
        }}
      >
        <Sparkles className="w-6 h-6" />
      </motion.div>

      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Save the Date Badge */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8"
        >
          <div className="inline-flex items-center bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-white/30">
            <span className="font-didot text-rose-gold font-medium tracking-wider text-sm">
              SAVE THE DATE
            </span>
          </div>
        </motion.div>

        {/* Wedding Letter - Envelope Style */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="max-w-4xl mx-auto relative"
        >
          {/* Envelope */}
          <motion.div
            className="relative cursor-pointer group"
            onClick={() => setIsLetterOpen(!isLetterOpen)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Envelope Back */}
            <div className="bg-gradient-to-br from-champagne to-blush p-8 rounded-lg shadow-2xl relative overflow-hidden min-h-[400px]">
              {/* Envelope Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-4 left-4 text-rose-gold text-2xl">💌</div>
                <div className="absolute top-4 right-4 text-sage text-2xl">🌿</div>
                <div className="absolute bottom-4 left-4 text-champagne text-2xl">✨</div>
                <div className="absolute bottom-4 right-4 text-blush text-2xl">💕</div>
              </div>
              
              {/* Envelope Front Flap */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-rose-gold/20 to-champagne/30 rounded-lg flex items-center justify-center"
                animate={isLetterOpen ? { 
                  rotateX: -180,
                  transformOrigin: "top"
                } : { 
                  rotateX: 0 
                }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
                style={{ 
                  transformStyle: "preserve-3d",
                  backfaceVisibility: "hidden"
                }}
              >
                {/* Wax Seal */}
                <motion.div
                  className="w-16 h-16 bg-rose-gold rounded-full flex items-center justify-center shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <Heart className="w-8 h-8 text-white" />
                </motion.div>
              </motion.div>

              {/* Letter Content - Shows when opened */}
              <AnimatePresence>
                {isLetterOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 20, scale: 0.9 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="relative z-10 bg-white/95 backdrop-blur-sm rounded-lg p-8 shadow-inner"
                  >
                    {/* Letter Header */}
                    <div className="text-center mb-6">
                      <motion.p
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.5 }}
                        className="text-sage font-playfair font-medium text-lg mb-4 tracking-wider"
                      >
                        CHÚNG TÔI SẮP KẾT HÔN
                      </motion.p>
                      
                      <motion.h1
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                        className="font-great-vibes text-4xl sm:text-5xl md:text-6xl font-normal text-gray-800 mb-4"
                      >
                        Minh & Hương
                      </motion.h1>
                      
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.9 }}
                        className="mb-6"
                      >
                        <p className="text-gray-600 font-playfair text-lg mb-2">Ngày cưới</p>
                        <p className="font-dancing text-2xl sm:text-3xl font-semibold text-gray-800">
                          15 tháng 8, 2024
                        </p>
                      </motion.div>
                    </div>

                    {/* Letter Body */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1.1 }}
                      className="space-y-4 text-center"
                    >
                      <p className="font-playfair text-gray-600 leading-relaxed">
                        Với niềm hạnh phúc và vinh dự, chúng tôi xin kính mời bạn đến dự
                      </p>
                      
                      <div className="grid md:grid-cols-2 gap-4 my-6">
                        <div className="bg-rose-gold/10 rounded-lg p-4">
                          <h3 className="font-dancing text-xl text-rose-gold mb-2">Lễ Vu Quy</h3>
                          <p className="font-playfair text-sm text-gray-600">
                            09:00 AM - Nhà gái<br/>
                            123 Đường ABC, Hà Nội
                          </p>
                        </div>
                        <div className="bg-sage/10 rounded-lg p-4">
                          <h3 className="font-dancing text-xl text-sage mb-2">Tiệc Cưới</h3>
                          <p className="font-playfair text-sm text-gray-600">
                            18:00 PM<br/>
                            Trung tâm Hội nghị Quốc gia, Hà Nội
                          </p>
                        </div>
                      </div>
                      
                      <p className="font-playfair text-gray-600 italic text-sm">
                        "Sự hiện diện của bạn sẽ là niềm hạnh phúc lớn nhất của chúng tôi"
                      </p>
                      
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-rose-gold text-white px-6 py-3 rounded-full font-playfair font-medium shadow-lg hover:bg-rose-gold/90 transition-all duration-300 mt-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          document.getElementById('rsvp')?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      >
                        Xác Nhận Tham Dự
                      </motion.button>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* Closed Letter Preview */}
              {!isLetterOpen && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center text-white relative z-10 flex flex-col items-center justify-center h-full"
                >
                  <p className="font-playfair text-lg mb-2">Wedding Invitation</p>
                  <p className="font-great-vibes text-3xl mb-4">Minh & Hương</p>
                  <p className="font-playfair text-sm opacity-80">Click to open letter</p>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
