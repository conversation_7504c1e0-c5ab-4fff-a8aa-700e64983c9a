import { motion } from 'framer-motion';
import { useState } from 'react';
import { Credit<PERSON><PERSON>, Co<PERSON>, Check, Gift, Heart } from 'lucide-react';

const GiftMoney = ({ id }) => {
  const [copiedAccount, setCopiedAccount] = useState(null);

  const bankAccounts = [
    {
      bank: "Vietcombank",
      accountNumber: "*************",
      accountName: "NGUYEN VAN MINH",
      qrCode: "/api/placeholder/200/200", // QR code placeholder
      color: "from-rose-gold to-champagne"
    },
    {
      bank: "Techcombank",
      accountNumber: "*************",
      accountName: "LE THI HUONG",
      qrCode: "/api/placeholder/200/200", // QR code placeholder
      color: "from-sage to-blush"
    }
  ];

  const copyToClipboard = (text, accountIndex) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedAccount(accountIndex);
      setTimeout(() => setCopiedAccount(null), 2000);
    });
  };

  return (
    <section id={id} className="py-20 bg-white/30">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
            className="text-5xl mb-4"
          >
            🎁
          </motion.div>

          <h2 className="font-didot text-5xl font-bold text-gray-800 mb-4 tracking-wide">
            Gửi Mừng Cưới
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Thay vì hoa và quà tặng, chúng tôi sẽ rất biết ơn nếu bạn gửi mừng qua tài khoản ngân hàng
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {bankAccounts.map((account, index) => (
            <motion.div
              key={account.bank}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="relative"
            >
              <div className="wedding-card p-8 relative overflow-hidden">
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${account.color} opacity-10 rounded-3xl`} />

                {/* Decorative elements */}
                <motion.div
                  className="absolute top-4 right-4 text-2xl"
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                >
                  💳
                </motion.div>

                <motion.div
                  className="absolute bottom-4 left-4 text-rose-gold/30 text-xl"
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  ✨
                </motion.div>

                <div className="relative z-10">
                  {/* Bank Header */}
                  <motion.div
                    className="text-center mb-6"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center justify-center mb-3">
                      <CreditCard className="w-8 h-8 text-rose-gold mr-3" />
                      <h3 className="font-didot text-2xl font-bold text-gray-800 tracking-wide">
                        {account.bank}
                      </h3>
                    </div>
                  </motion.div>

                  {/* QR Code */}
                  <motion.div
                    className="text-center mb-6"
                    whileHover={{ scale: 1.05 }}
                  >
                    <div className="w-48 h-48 mx-auto bg-white rounded-2xl shadow-lg flex items-center justify-center border-4 border-gray-100">
                      <div className="text-gray-400 text-center">
                        <div className="w-32 h-32 bg-gray-100 rounded-lg mb-2 flex items-center justify-center">
                          <span className="text-4xl">📱</span>
                        </div>
                        <p className="text-sm font-didot">QR Code</p>
                      </div>
                    </div>
                    <p className="text-gray-600 font-didot text-sm mt-2">
                      Quét mã QR để chuyển khoản nhanh
                    </p>
                  </motion.div>

                  {/* Account Details */}
                  <div className="space-y-4 mb-6">
                    <motion.div
                      className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-didot text-sm text-gray-600 mb-1">
                            Số tài khoản
                          </p>
                          <p className="font-didot text-lg font-bold text-gray-800 tracking-wider">
                            {account.accountNumber}
                          </p>
                        </div>
                        <motion.button
                          onClick={() => copyToClipboard(account.accountNumber, index)}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-2 rounded-full bg-rose-gold/10 hover:bg-rose-gold/20 transition-colors"
                        >
                          {copiedAccount === index ? (
                            <Check className="w-5 h-5 text-green-600" />
                          ) : (
                            <Copy className="w-5 h-5 text-rose-gold" />
                          )}
                        </motion.button>
                      </div>
                    </motion.div>

                    <motion.div
                      className="bg-white/50 rounded-2xl p-4 backdrop-blur-sm border border-white/30"
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-didot text-sm text-gray-600 mb-1">
                            Chủ tài khoản
                          </p>
                          <p className="font-didot text-lg font-bold text-gray-800">
                            {account.accountName}
                          </p>
                        </div>
                        <motion.button
                          onClick={() => copyToClipboard(account.accountName, `name-${index}`)}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-2 rounded-full bg-sage/10 hover:bg-sage/20 transition-colors"
                        >
                          {copiedAccount === `name-${index}` ? (
                            <Check className="w-5 h-5 text-green-600" />
                          ) : (
                            <Copy className="w-5 h-5 text-sage" />
                          )}
                        </motion.button>
                      </div>
                    </motion.div>
                  </div>

                  {/* Copy All Button */}
                  <motion.button
                    onClick={() => {
                      const fullInfo = `${account.bank}\nSTK: ${account.accountNumber}\nTên: ${account.accountName}`;
                      copyToClipboard(fullInfo, `all-${index}`);
                    }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-rose-gold text-white py-3 px-6 rounded-2xl font-didot font-medium text-center flex items-center justify-center space-x-2 hover:bg-rose-gold/90 transition-all duration-300 shadow-lg"
                  >
                    <Gift className="w-5 h-5" />
                    <span>
                      {copiedAccount === `all-${index}` ? 'Đã Sao Chép!' : 'Sao Chép Thông Tin'}
                    </span>
                  </motion.button>

                  {/* Decorative divider */}
                  <motion.div
                    className="flex items-center justify-center mt-6"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                    <span className="mx-3 text-champagne text-lg">💝</span>
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-rose-gold to-transparent" />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Thank You Message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="wedding-card max-w-3xl mx-auto p-8">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="text-4xl mb-4"
            >
              🙏
            </motion.div>

            <h3 className="font-didot text-2xl font-bold text-gray-800 mb-4 tracking-wide">
              Lời Cảm Ơn Chân Thành
            </h3>
            <p className="text-gray-600 font-didot leading-relaxed mb-4">
              Sự hiện diện của bạn trong ngày trọng đại là món quà ý nghĩa nhất đối với chúng tôi.
              Nếu bạn muốn gửi mừng, chúng tôi sẽ vô cùng biết ơn và trân trọng tấm lòng của bạn.
            </p>

            <motion.div
              className="flex justify-center space-x-3 text-2xl"
              animate={{ y: [0, -3, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span>💕</span>
              <span>🎁</span>
              <span>🌸</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default GiftMoney;
