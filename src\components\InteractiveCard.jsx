import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { Heart, Calendar, MapPin } from 'lucide-react';

const InteractiveCard = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <section className="py-20 flex items-center justify-center min-h-screen">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="font-didot text-3xl md:text-4xl font-bold text-gray-800 mb-4 tracking-wide">
            Thi<PERSON><PERSON>ư<PERSON>
          </h2>
          <p className="text-gray-600 text-lg">
            - Nhấp vào thiệp để mở -
          </p>
        </motion.div>

        <div className="flex justify-center">
          <motion.div
            className="relative cursor-pointer"
            style={{ perspective: "1000px" }}
            onClick={() => setIsOpen(!isOpen)}
          >
            {/* Card Container */}
            <motion.div
              className="relative w-80 h-96 md:w-96 md:h-[500px]"
              animate={{ rotateY: isOpen ? 180 : 0 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
              style={{ transformStyle: "preserve-3d" }}
            >
              {/* Front of Card */}
              <motion.div
                className="absolute inset-0 backface-hidden"
                style={{ backfaceVisibility: "hidden" }}
              >
                <div className="w-full h-full bg-gradient-to-br from-rose-gold via-champagne to-blush rounded-3xl shadow-2xl border-4 border-white/50 overflow-hidden">
                  {/* Decorative Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 left-4 text-white text-6xl">🌸</div>
                    <div className="absolute top-4 right-4 text-white text-6xl">🌸</div>
                    <div className="absolute bottom-4 left-4 text-white text-6xl">🌿</div>
                    <div className="absolute bottom-4 right-4 text-white text-6xl">🌿</div>
                  </div>
                  
                  <div className="relative z-10 h-full flex flex-col items-center justify-center p-8 text-center">
                    <motion.div
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 3, repeat: Infinity }}
                      className="mb-6"
                    >
                      <Heart className="w-16 h-16 text-white mx-auto mb-4" />
                    </motion.div>
                    
                    <h3 className="font-carolyna text-3xl md:text-4xl font-bold text-white mb-4">
                      Minh & Hương
                    </h3>
                    
                    <div className="text-white/90 font-didot text-lg mb-6">
                      <div className="flex items-center justify-center mb-2">
                        <Calendar className="w-5 h-5 mr-2" />
                        <span>15.08.2024</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <MapPin className="w-5 h-5 mr-2" />
                        <span>Hà Nội</span>
                      </div>
                    </div>
                    
                    <motion.div
                      animate={{ y: [0, -5, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="text-white/80 text-sm font-didot"
                    >
                      Nhấp để mở thiệp
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Back of Card */}
              <motion.div
                className="absolute inset-0 backface-hidden"
                style={{ 
                  backfaceVisibility: "hidden",
                  transform: "rotateY(180deg)"
                }}
              >
                <div className="w-full h-full bg-white rounded-3xl shadow-2xl border-4 border-rose-gold/30 overflow-hidden">
                  <div className="h-full flex flex-col items-center justify-center p-8 text-center">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: isOpen ? 1 : 0, y: isOpen ? 0 : 20 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      <h3 className="font-didot text-2xl font-bold text-gray-800 mb-4 tracking-wide">
                        Trân Trọng Kính Mời
                      </h3>
                      
                      <div className="mb-6">
                        <p className="font-didot text-lg text-gray-700 mb-2">
                          Lễ Thành Hôn
                        </p>
                        <p className="font-carolyna text-3xl font-bold text-rose-gold mb-2">
                          Minh & Hương
                        </p>
                        <p className="font-didot text-gray-600">
                          Vào lúc 18h00 - Thứ Bảy
                        </p>
                        <p className="font-didot text-xl font-semibold text-gray-800">
                          15 tháng 8, 2024
                        </p>
                      </div>
                      
                      <div className="text-gray-600 font-didot text-sm">
                        <p className="mb-1">Tại:</p>
                        <p className="font-medium">Trung tâm Hội nghị Quốc gia</p>
                        <p>Số 1 Thăng Long, Ba Đình, Hà Nội</p>
                      </div>
                      
                      <motion.div
                        className="mt-6 flex justify-center space-x-2 text-2xl"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <span>💕</span>
                        <span>🌸</span>
                        <span>✨</span>
                      </motion.div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="text-center mt-8"
        >
          <p className="text-gray-600 font-didot">
            {isOpen ? "Nhấp lại để đóng thiệp" : "Nhấp vào thiệp để xem lời mời"}
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default InteractiveCard;
