import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
import { Clock, MapPin, Users, Camera, Music, Utensils } from 'lucide-react';

const TimelineItem = ({ item, index }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      className={`flex items-center mb-12 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
    >
      <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
        <motion.div
          whileHover={{ scale: 1.02 }}
          className="wedding-card p-6"
        >
          <div className={`flex items-center mb-3 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
            <div className="text-rose-gold mr-2">
              {item.icon}
            </div>
            <h3 className="font-serif text-xl font-semibold text-gray-800">
              {item.title}
            </h3>
          </div>
          
          <div className={`flex items-center mb-2 text-gray-600 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
            <Clock className="w-4 h-4 mr-1" />
            <span className="text-sm">{item.time}</span>
          </div>
          
          <div className={`flex items-center mb-3 text-gray-600 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
            <MapPin className="w-4 h-4 mr-1" />
            <span className="text-sm">{item.location}</span>
          </div>
          
          <p className="text-gray-700 leading-relaxed">
            {item.description}
          </p>
        </motion.div>
      </div>
      
      <div className="relative">
        <motion.div
          initial={{ scale: 0 }}
          animate={isInView ? { scale: 1 } : { scale: 0 }}
          transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
          className="w-4 h-4 bg-rose-gold rounded-full border-4 border-white shadow-lg z-10 relative"
        />
      </div>
      
      <div className="w-1/2" />
    </motion.div>
  );
};

const Timeline = () => {
  const timelineEvents = [
    {
      title: "Lễ Vu Quy",
      time: "08:00 - 10:00",
      location: "Nhà Cô Dâu - 123 Phố Huế, Hà Nội",
      description: "Lễ vu quy tại nhà cô dâu với sự tham gia của gia đình hai bên và bạn bè thân thiết.",
      icon: <Users className="w-5 h-5" />
    },
    {
      title: "Rước Dâu",
      time: "10:30 - 12:00",
      location: "Từ nhà Cô Dâu đến nhà Chú Rể",
      description: "Đoàn rước dâu khởi hành từ nhà cô dâu về nhà chú rể với đoàn xe hoa rực rỡ.",
      icon: <Camera className="w-5 h-5" />
    },
    {
      title: "Lễ Thành Hôn",
      time: "14:00 - 16:00",
      location: "Nhà Chú Rể - 456 Láng Hạ, Hà Nội",
      description: "Lễ thành hôn trang trọng với sự chứng kiến của gia đình và bạn bè.",
      icon: <Music className="w-5 h-5" />
    },
    {
      title: "Tiệc Cưới",
      time: "18:00 - 21:00",
      location: "Trung tâm Hội nghị Quốc gia",
      description: "Tiệc cưới hoành tráng với menu cao cấp và chương trình văn nghệ đặc sắc.",
      icon: <Utensils className="w-5 h-5" />
    }
  ];

  return (
    <section id="timeline" className="py-20 relative">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="font-serif text-5xl font-bold text-gray-800 mb-4">
            Lịch Trình Cưới
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Cùng chúng tôi trải qua những khoảnh khắc đáng nhớ trong ngày trọng đại
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-0.5 bg-gradient-to-b from-rose-gold via-champagne to-sage h-full" />
          
          {timelineEvents.map((event, index) => (
            <TimelineItem key={index} item={event} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Timeline;
