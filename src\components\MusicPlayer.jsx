import { motion, AnimatePresence } from 'framer-motion';
import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Music } from 'lucide-react';

const MusicPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const audioRef = useRef(null);

  // Placeholder audio URL - replace with actual wedding music
  const audioUrl = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav";

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = 0.3; // Set default volume to 30%
    }
  }, []);

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch(e => {
          console.log("Audio play failed:", e);
        });
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  return (
    <>
      {/* Audio element */}
      <audio
        ref={audioRef}
        loop
        preload="metadata"
        onEnded={() => setIsPlaying(false)}
        onError={() => setIsPlaying(false)}
      >
        <source src={audioUrl} type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      {/* Music Player UI */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 100 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 100 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="fixed bottom-6 right-6 z-50"
          >
            <div className="wedding-card p-4 flex items-center space-x-3 shadow-2xl">
              {/* Music Icon with Animation */}
              <motion.div
                animate={isPlaying ? {
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                } : {}}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-rose-gold"
              >
                <Music className="w-5 h-5" />
              </motion.div>

              {/* Song Info */}
              <div className="hidden sm:block">
                <p className="font-didot text-sm font-medium text-gray-800">
                  Nhạc Cưới
                </p>
                <p className="font-didot text-xs text-gray-600">
                  {isPlaying ? "Đang phát..." : "Tạm dừng"}
                </p>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-2">
                {/* Play/Pause Button */}
                <motion.button
                  onClick={togglePlay}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-rose-gold text-white rounded-full flex items-center justify-center hover:bg-rose-gold/90 transition-colors shadow-lg"
                >
                  {isPlaying ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4 ml-0.5" />
                  )}
                </motion.button>

                {/* Mute Button */}
                <motion.button
                  onClick={toggleMute}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-8 h-8 bg-sage/20 text-sage rounded-full flex items-center justify-center hover:bg-sage/30 transition-colors"
                >
                  {isMuted ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </motion.button>
              </div>

              {/* Visualizer */}
              {isPlaying && (
                <div className="flex items-center space-x-1">
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="w-1 bg-rose-gold rounded-full"
                      animate={{
                        height: [8, 16, 8],
                        opacity: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 0.8,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toggle Button when hidden */}
      {!isVisible && (
        <motion.button
          onClick={toggleVisibility}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="fixed bottom-6 right-6 z-50 w-12 h-12 bg-rose-gold text-white rounded-full flex items-center justify-center shadow-2xl"
        >
          <Music className="w-5 h-5" />
        </motion.button>
      )}

      {/* Close button when visible */}
      {isVisible && (
        <motion.button
          onClick={toggleVisibility}
          whileHover={{ scale: 1.1, rotate: 90 }}
          whileTap={{ scale: 0.9 }}
          className="fixed bottom-6 right-20 z-50 w-8 h-8 bg-gray-400/20 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-400/30 transition-colors"
        >
          ×
        </motion.button>
      )}

      {/* Floating music notes when playing */}
      {isPlaying && (
        <div className="fixed inset-0 pointer-events-none z-40">
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute text-rose-gold/30 text-2xl"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + i * 10}%`,
              }}
              animate={{
                y: [0, -50, 0],
                opacity: [0, 1, 0],
                rotate: [0, 10, -10, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: i * 0.8
              }}
            >
              ♪
            </motion.div>
          ))}
        </div>
      )}
    </>
  );
};

export default MusicPlayer;
